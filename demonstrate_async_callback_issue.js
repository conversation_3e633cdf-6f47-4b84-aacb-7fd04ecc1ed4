#!/usr/bin/env node

/**
 * Demonstration of async callback response handling incompatibility 
 * in sendRequestToPlatform for CDP sessions
 * 
 * This code shows exactly why Apple Pay executor fails in Playwright sessions
 * but works in Selenium sessions.
 */

'use strict';

// Mock the key functions to demonstrate the issue
console.log('=== Demonstrating Async Callback Response Handling Incompatibility ===\n');

// 1. Mock isCDP function
const isCDP = (keyObject) => {
  return keyObject.isPlaywright === true || keyObject.isPuppeteer === 'true' || keyObject.isDetox === 'true';
};

// 2. Mock bridge.sendResponse (what Selenium sessions use)
const mockBridgeSendResponse = (keyObject, requestStateObj) => {
  console.log('📤 bridge.sendResponse called');
  console.log('   - This works for Selenium sessions (HTTP response)');
  console.log('   - This FAILS for CDP sessions (no HTTP response object)');
  
  if (isCDP(keyObject)) {
    console.log('   ❌ ERROR: CDP session detected but trying to send HTTP response!');
    console.log('   ❌ requestStateObj.onResolve callback is NEVER called');
    console.log('   ❌ Promise in Playwright handler NEVER resolves');
    return false;
  } else {
    console.log('   ✅ SUCCESS: Selenium session - HTTP response sent');
    return true;
  }
};

// 3. Original instrumentAndSendExecutorResponse (BEFORE our fix)
const originalInstrumentAndSendExecutorResponse = (executorType, keyObject, requestStateObj) => {
  console.log(`🔧 originalInstrumentAndSendExecutorResponse called for ${executorType}`);
  
  // This is what the original code does - ALWAYS calls bridge.sendResponse
  const success = mockBridgeSendResponse(keyObject, requestStateObj);
  
  if (!success && isCDP(keyObject)) {
    console.log('   💥 FAILURE: CDP session promise never resolves!');
  }
};

// 4. Fixed instrumentAndSendExecutorResponse (AFTER our fix)
const fixedInstrumentAndSendExecutorResponse = (executorType, keyObject, requestStateObj) => {
  console.log(`🔧 fixedInstrumentAndSendExecutorResponse called for ${executorType}`);
  
  // Enhanced CDP session handling
  if (isCDP(keyObject) && requestStateObj.onResolve) {
    console.log('   ✅ CDP session detected - using onResolve callback');
    const responseData = {
      sessionId: requestStateObj.clientSessionID || keyObject.rails_session_id,
      status: 0,
      value: null
    };
    requestStateObj.onResolve(JSON.stringify(responseData));
    console.log('   ✅ SUCCESS: onResolve called, promise will resolve');
    return;
  }
  
  // Standard path for Selenium sessions
  mockBridgeSendResponse(keyObject, requestStateObj);
};

// 5. Mock sendRequestToPlatform (the problematic async function)
const mockSendRequestToPlatform = async (executorType, serverURL, requestStateObj, keyObject, errorMessage, timeout, options, responseHandler) => {
  console.log(`🌐 sendRequestToPlatform called for ${executorType}`);
  console.log('   - Making async request to platform...');
  
  // Simulate async platform response
  setTimeout(() => {
    console.log('   - Platform responded with success');
    console.log('   - Calling responseHandler callback...');
    
    // This is where the issue occurs - the responseHandler calls instrumentAndSendExecutorResponse
    const mockResponse = { statusCode: 200, data: { success: true } };
    responseHandler(mockResponse);
  }, 100);
};

// 6. Mock Apple Pay handler (simplified)
const mockApplePayHandler = async (keyObject, requestStateObj, useFixedVersion = false) => {
  console.log(`🍎 Apple Pay handler called (${useFixedVersion ? 'FIXED' : 'ORIGINAL'} version)`);
  
  const responseHandler = (response) => {
    console.log('   📞 responseHandler callback triggered');
    
    if (response && response.statusCode === 200) {
      // This is the critical point where the issue occurs
      if (useFixedVersion) {
        fixedInstrumentAndSendExecutorResponse('applePay_custom_executor', keyObject, requestStateObj);
      } else {
        originalInstrumentAndSendExecutorResponse('applePay_custom_executor', keyObject, requestStateObj);
      }
    }
  };
  
  await mockSendRequestToPlatform(
    'applePay_custom_executor',
    '/execute_apple_pay',
    requestStateObj,
    keyObject,
    'internal_error',
    40000,
    {},
    responseHandler
  );
};

// 7. Test scenarios
const runDemo = async () => {
  console.log('🧪 SCENARIO 1: Selenium Session (Works with both versions)\n');

  const seleniumKeyObject = {
    rails_session_id: 'selenium-session-123',
    isPlaywright: false,
    device: 'iPhone 12'
  };

  const seleniumRequestStateObj = {
    clientSessionID: 'selenium-session-123',
    // No onResolve callback for Selenium sessions
  };

  await mockApplePayHandler(seleniumKeyObject, seleniumRequestStateObj, false);

console.log('\n' + '='.repeat(60) + '\n');

console.log('🧪 SCENARIO 2: Playwright Session - ORIGINAL (Fails)\n');

const playwrightKeyObject = {
  rails_session_id: 'playwright-session-456',
  isPlaywright: true,
  device: 'iPhone 12'
};

let promiseResolved = false;
const playwrightRequestStateObj = {
  clientSessionID: 'playwright-session-456',
  onResolve: (response) => {
    console.log('   🎉 onResolve callback called with:', response);
    promiseResolved = true;
  }
};

await mockApplePayHandler(playwrightKeyObject, playwrightRequestStateObj, false);

setTimeout(() => {
  console.log(`\n   Promise resolved: ${promiseResolved ? '✅ YES' : '❌ NO'}`);
  if (!promiseResolved) {
    console.log('   💥 This is why Apple Pay fails in Playwright sessions!');
  }
}, 200);

console.log('\n' + '='.repeat(60) + '\n');

console.log('🧪 SCENARIO 3: Playwright Session - FIXED (Works)\n');

promiseResolved = false;
const fixedPlaywrightRequestStateObj = {
  clientSessionID: 'playwright-session-789',
  onResolve: (response) => {
    console.log('   🎉 onResolve callback called with:', response);
    promiseResolved = true;
  }
};

await mockApplePayHandler(playwrightKeyObject, fixedPlaywrightRequestStateObj, true);

setTimeout(() => {
  console.log(`\n   Promise resolved: ${promiseResolved ? '✅ YES' : '❌ NO'}`);
  if (promiseResolved) {
    console.log('   🎉 This is how our fix resolves the issue!');
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 SUMMARY:');
  console.log('• Selenium sessions: Work because they use HTTP responses');
  console.log('• Playwright sessions (original): Fail because onResolve is never called');
  console.log('• Playwright sessions (fixed): Work because we detect CDP and call onResolve');
  console.log('• Root cause: sendRequestToPlatform async callbacks incompatible with CDP sessions');
}, 300);
