/* eslint no-fallthrough: 0 */

const http = require("http");
const HubLogger = require("./log");
const constants = require("./constants");
const helper = require("./helper");
const requestlib = require("./lib/request");
const Promise = require("bluebird");
const util = require("util");
const uuidv4 = require('uuid/v4');
const queueHandler = require("./queueHandler");
const unflatten = require("flat").unflatten;
const railsPipeline = require("./railsRequests/railsPipeline");
const querystring = require('querystring');
const { Events } = require('browserstack-dwh');
const { configurePrivoxyTimeout } = require('./privoxy');
const { extractIntVersion, isNotUndefined, isUndefined, isEdgeChromium, isEdgeButNonChromiumBased, isTrueString, isFalseString } = require('./typeSanity');
const { REMOTE_DEBUGGER_PORT, PLAYWRIGHT, PUPPETEER } = require('./config/socketConstants');
const { extractPlaywrightConfig } = require('./socketManagers/validations');
const { isCDP } = require('./socketManagers/validations');
const healthCheck = require('./healthCheck');
const AICommandHelper = require("./controllers/seleniumCommand/helpers/AICommandHelper");

const LL = constants.LOG_LEVEL;


const deleteSeleniumBrowserVersionIfRequired = (payload, seleniumVersion) => {
  const { desiredCapabilities = {}, capabilities = {} } = payload;
  const { firstMatch: [firstMatchEntry = {}] = [] } = capabilities;
  const browser = capabilities.browserName || desiredCapabilities.browserName || desiredCapabilities.browser;
  if (((desiredCapabilities.os || '').match(/os\s+x/i) && (browser || '').match(/safari/i)) || (isNotUndefined(seleniumVersion) &&  helper.validateDeleteBrowserVersion(seleniumVersion))) {
    delete desiredCapabilities.version;
    delete firstMatchEntry.browserVersion;
  }
  return payload;
};

// TODO: Refactor of this file is not completed yet.
// _postBrowserStack and related functions should be broken down to smaller
// composable functions for easier unit testing.
// Also the _postBrowserStack retry code can be refactored to use the
// inbuilt requestlib retry mechanism.

// postBrowserStack calls will go via railsPipeline and will be queued
// _postBrowserStack calls will not use railsPipeline

// TODO: Move to railsInterface
exports.postBrowserStack = function(url, post_params, request, response, callback, rails_omitted_caps, queue_id, attempt, firecmd_attempt, start_attempt, error, indexCounter, retry, skipResponseClientDisconnect = false) {
  if (isUndefined(retry)) {
    const retryMethod = constants.DEFAULT_HUB_TO_BROWSERSTACK_RETRY_METHOD;
    const attempt = 0;
    retry = {
      maxRetryCount: constants.HUB_TO_BROWSERSTACK_RETRY[retryMethod].maxRetryCount,
      requestDelay: generateRequestDelay(retryMethod, attempt),
      retryMethod: retryMethod,
      maxRequestLife: constants.HUB_TO_BROWSERSTACK_RETRY[retryMethod].maxRequestLife,
      attempt: 0
    };
  }
  const isStopRequest = url.match(/stop=/) || url.match(/release=/);
  const isMediaRequest = url.match(/resolve_media/);
  const isMarkAsPercyRequest = url.match(/mark_as_percy=true/);
  const isStartRequest = post_params.start == 'true';
  const sessionId = isStopRequest ? querystring.parse(url).k : undefined;
  const isMobile = helper.isMobile(post_params);

  if (!attempt) {
    attempt = 1;
  }

  post_params.pipelineIdentifier = helper.randomID(20);
  post_params.selauth_request_ever_queued = post_params.selauth_request_ever_queued || false;

  helper.addToConsoleTimes(request, 'rails-defender');
  if (attempt > 2) {
    HubLogger.bsErrorHandler(
      request,
      response,
      error,
      `BS threw error ${attempt} time${attempt > 1 ? 's' : ''} for single request`,
      (url + " : " + JSON.stringify(request ? request.headers : {}) + " : " + JSON.stringify(post_params)),
      post_params && post_params['u'],
      { isAppAutomate: post_params.isAppAutomate,
        isStartRequest: isStartRequest,
        isDetox: (post_params["framework"] === constants.DETOX) }
    );
    if (isStopRequest) {
      if (sessionId) {
        helper.handleSessionsWithStopFailedOnMaxRetries(sessionId, url.match(/app=true/));
      }
      if(callback) callback("{}");
    }

    if(isMediaRequest){
      if(callback) callback(JSON.stringify({resolve_media: "failed"}));
    }

    if (isMarkAsPercyRequest) {
      if(callback) callback(JSON.stringify({ success: false, message: error }));
    }
    return;
  }

  let requestId;
  let requestReceivedAt;
  if (isNotUndefined(request)) {
    requestId = request.id;
    requestReceivedAt = request.requestReceivedAt;
  }

  if (response && !response.clientOnline() && isStartRequest) {
    // When client disconnects even before being pushed into rails request pipeline during start request to rails, we simply log this as an error and do not proceed with the request to Rails.
    return handle_user_disconnected_during_start(post_params, request, response, skipResponseClientDisconnect, firecmd_attempt, start_attempt, retry, requestId, "Pre-queue disconnect", false);
  }

  // We store non-critical pipeline related metadata here
  const railsPipelineExtraData = {
    isQueued: post_params.selauth_request_ever_queued || false,
    isMobile,
  };

  var local = false;
  if (post_params.local)
    local = post_params.local;

  HubLogger.miscLogger(`postBrowserStack_${post_params.pipelineIdentifier}`, `pushing to pipeline identifier: ${post_params.pipelineIdentifier} requestId: ${requestId}  session id: ${post_params["automation_session_id"] || sessionId || 'not-available'}, appautomate: ${post_params.isAppAutomate}`, LL.INFO);
  railsPipeline.railsRequestPipeline({
    identifier: post_params.pipelineIdentifier,
    sessionId,
    isStopRequest,
    isStartRequest,
    username: post_params['u'],
    password: post_params['password'],
    local,
    response,
    isAppAutomateSession: post_params.isAppAutomate,
    railsPipelineExtraData,
    requestId,
    requestReceivedAt,
    capabilities: post_params.desiredCapabilities,
    rawCapabilities: post_params.rawCapabilities
  }).then((shouldDrop) => {
    helper.addToConsoleTimes(request, 'rails-defender-end');

    //Instrumentation to check if client is not online when picked from pipeline
    if(isStartRequest && response && !response.clientOnline()){
      // When client disconnects when picked from rails request pipeline during start request to rails, we simply log this as an error and do not proceed with the request to Rails.
      let reqReceivedAtPipeline = (requestReceivedAt) ? new Date(requestReceivedAt).toISOString() : 'undefined';
      helper.PingZombie({
        'kind': 'user-not-online-pipeline',
        'session_id': post_params["automation_session_id"],
        'user_id': post_params['u'],
        'data': 'user_id: '+post_params['u']+', requestReceivedAtPipeline: '+reqReceivedAtPipeline+', isAppAutomate: '+post_params.isAppAutomate+', requestId: '+requestId+', selauth_request_ever_queued: ' + post_params['selauth_request_ever_queued']
      });
      return handle_user_disconnected_during_start(post_params, request, response, skipResponseClientDisconnect, firecmd_attempt, start_attempt, retry, requestId, "Inside railsRequestPipeline", true);
    }

    // If the request was queued before, tell it to rails as well
    if(railsPipelineExtraData.isQueued && isStartRequest) {
      post_params.selauth_request_ever_queued = true;
    }
    // shouldDrop is true only in case a cached response was sent for the request
    if (!shouldDrop) {
      _postBrowserStack(url, post_params, request, response, callback, error, rails_omitted_caps, indexCounter, firecmd_attempt, queue_id, start_attempt, retry);
    }
  });
};

var handle_user_disconnected_during_start = function(post_params, request, response, skipResponseClientDisconnect, firecmd_attempt, start_attempt, retry, requestId, msg = "", decrementTrackSet=false) {
  HubLogger.miscLogger(`userDisconnect_${post_params['u']}`, `${msg}. Not requesting rails as client is not longer waiting on it. Decreasing queue count. ${request.id}, isAppAutomate - ${post_params.isAppAutomate}`, LL.INFO);
    helper.redisClientSecond.hincrby(constants.USER_TERMINATED_SESSIONS, `queue::${queueHandler.getQueueProduct(post_params)}dropped::${post_params.u}`, 1);
    if(decrementTrackSet){
      //removing req from trackset
      helper.unregisterDroppedRailsRequestFromTrackSet(post_params.pipelineIdentifier, post_params.isAppAutomate);
    }
    helper.respondWithError(request, response, 'ClientNotOnline', true, skipResponseClientDisconnect);
    const isMobile = helper.isMobile(post_params);
    const hoothootPlatform = post_params.isAppAutomate ? 'all' : isMobile ? 'mobile' : 'desktop';
    HubLogger.hoothoot_user.uniqueUserEvent(post_params['u'], (post_params.isAppAutomate ? 'app-automate' : 'automate'), 'client_disconnect', hoothootPlatform, helper.getHubCanaryTag());
    if (!post_params.isAppAutomate) HubLogger.hoothoot_use.emit('automate_errors_data', 1, { event_type: 'client_disconnect', platform: hoothootPlatform, product: 'automate', user: post_params['u'], hub_region: constants.region});
    if (post_params["automation_session_id"]) {
      helper.pushToCLS('user_disconnected_before_retry', {
        session_id: post_params["automation_session_id"],
        user_id: post_params['u'],
        firecmd_attempt: firecmd_attempt,
        start_req_attempt: start_attempt,
        retry: retry,
        requestId: requestId,
        message: msg
      }, post_params.isAppAutomate);
    }
    helper.sendToEDS({
      kind: post_params.isAppAutomate ? Events.APP_AUTOMATE_ERROR_DATA : Events.AUTOMATE_ERROR_DATA,
      error_code_str: "user-not-online-pipeline",
      error_message: {
        caps: (post_params.desiredCapabilities) ? JSON.parse(decodeURIComponent(post_params.desiredCapabilities)) : 'undefined',
        firecmd_attempt: firecmd_attempt,
        start_req_attempt: start_attempt,
        retry: retry,
        username: post_params['u']
      },
      raw_capabilities: (post_params.rawCapabilities) ? JSON.parse(decodeURIComponent(post_params.rawCapabilities)) : 'undefined',
      request_received_at: request.requestReceivedAt,
      request_id: requestId,
    });
    helper.addToConsoleTimes(request, 'time-in-queue-end');
    if (request.aborted) {
      HubLogger.miscLogger('REQUEST_ABORTED', `Request ${request.id} is aborted.`, LL.INFO);
      helper.markRequestEnd(request);
    }
    return "user_disconnected_before_retry";
};
exports.handle_user_disconnected_during_start = handle_user_disconnected_during_start;

const getBrowserPath = (lcaps, browserObj) => {
  let k = lcaps["orig_os"].match(/mac/) ? "command_mac" : "command";
  let browserPath = null;
  if (k == "command") {
    browserPath = browserObj[k].split("\"")[3].trim();
    if (lcaps["64bit"] && lcaps["64bit"].toString() == "true") {
      browserPath = browserPath.replace("Program Files", "Program Files (x86)");
    }
  } else {
    browserPath = browserObj[k].split(" -")[0].trim().replace(/\\/gi, '');
  }
  return browserPath;
};

var fixCapabilitiesForOneBrowser = function(lcaps, browserstackParams, browser, suffix) {
  const sessionId = (browserstackParams['browserstack.video.filename'] || '').replace(/video-/g, '');
  if (lcaps["browserName"] == browser && (isUndefined(lcaps["marionette"]) || lcaps["marionette"].toString().toLowerCase() !== "true")) {
    var release = lcaps["browser_version"].toString().match(/beta$/) ? 'beta': 'stable';
    for (var i=0; i < constants.browserConfig[browser].length; i++) {
      if (constants.browserConfig[browser][i].release === release && constants.browserConfig[browser][i].version === lcaps["version"].replace('beta','').trim()) {
        let browserPath = getBrowserPath(lcaps, constants.browserConfig[browser][i]);
        lcaps[browser + suffix] = browserPath;
        if(browser == "chrome" || isEdgeChromium(browser, lcaps.version)){
          lcaps["chromeOptions"] = lcaps["chromeOptions"] || {};
          lcaps["chromeOptions"]["binary"] = browserPath;
        }
        if(lcaps && lcaps["chromeOptions"] && lcaps["chromeOptions"]["extensions"] && Array.isArray(lcaps["chromeOptions"]["extensions"]) && lcaps["chromeOptions"]["extensions"].length > 0) {
          HubLogger.miscLogger('fixCapabilitiesForOneBrowser', `Customer Sent a custom chrome extension of length ${lcaps.chromeOptions.extensions[0].length} sessionID ${sessionId}`, LL.INFO);
          helper.sendToEDS({
            kind: Events.AUTOMATE_TEST_SESSIONS,
            hashed_id: sessionId,
            secondary_diagnostic_reasons: 'custom-chrome-extension',
          });
        }
        break;
      }
    }
  }
};

var chromeOptionsPrefs = function(lcaps){
  if(helper.isHash(lcaps["chromeOptions"]["prefs"])) {
    lcaps["chromeOptions"]["prefs"] = unflatten(lcaps["chromeOptions"]["prefs"]);
  }

  removeChromeOptionsDownloadLocation(lcaps);

  var newPrefs = {};

  if (((helper.isDefined(lcaps.browserName) && lcaps.browserName.toLowerCase() === 'edge') || (helper.isDefined(lcaps.browser) && lcaps.browser.toLowerCase() === 'edge')) && (lcaps.version === constants.edge91BetaBrowserPreference || lcaps.browser_version === constants.edge91BetaBrowserPreference || lcaps.browserVersion === constants.edge91BetaBrowserPreference)) {
    newPrefs.browser = { show_update_promotion_info_bar: false, check_default_browser: false, shown_close_prompt_promotion: true };
  } else {
    newPrefs.browser = { show_update_promotion_info_bar: false, check_default_browser: false };
  }

  newPrefs.devtools = { preferences: { cacheDisabled: true } };

  const promptForDownload = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'download', 'prompt_for_download']);
  if (helper.isDefined(promptForDownload)) {
    newPrefs.download = {
      prompt_for_download: promptForDownload,
    };
  }

  newPrefs.profile = {
    password_manager_enabled: false,
  };

  const profilePrefs = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'profile']);
  if(helper.isHash(profilePrefs)) {
    constants.whitelisted_chromeoptions_profile_keys.forEach(function(profile_key) {
      if (profile_key in profilePrefs) {
        newPrefs.profile[profile_key] = profilePrefs[profile_key];
      }
    });
  }

  const safeBrowsingEnabled = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'safebrowsing', 'enabled']);
  if (helper.isDefined(safeBrowsingEnabled)) {
    newPrefs.safebrowsing = {
      enabled: safeBrowsingEnabled,
    };
  }

  const alwaysOpenPdf = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'plugins', 'always_open_pdf_externally']);
  if (helper.isDefined(alwaysOpenPdf)) {
    newPrefs.plugins = newPrefs.plugins || {};
    newPrefs.plugins.always_open_pdf_externally = alwaysOpenPdf;
  }

  const disabledPlugins = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'plugins', 'plugins_disabled']);
  if (typeof disabledPlugins === 'object' && Array.isArray(disabledPlugins)) {
    newPrefs.plugins = newPrefs.plugins || {};
    newPrefs.plugins.plugins_disabled = disabledPlugins;
  }

  const credentialsEnableService = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'credentials_enable_service']);
  if (helper.isDefined(credentialsEnableService)) {
    newPrefs.credentials_enable_service = credentialsEnableService;
  }

  const passwordManagerEnabled = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'password_manager_enabled']);
  if (helper.isDefined(passwordManagerEnabled)) {
    newPrefs.password_manager_enabled = passwordManagerEnabled;
  }

  const acceptLanguages = helper.nestedKeyValue(lcaps, ['chromeOptions', 'prefs', 'intl', 'accept_languages']);
  if(helper.isDefined(acceptLanguages)) {
    newPrefs.intl = {
      accept_languages: acceptLanguages,
    };
  }

  if(helper.isDefined(lcaps.browserName) && lcaps.browserName.toLowerCase() === 'edge' && parseFloat(lcaps["browser_version"] || lcaps["version"]) > 113){
    newPrefs.user_experience_metrics = {
      personalization_data_consent_enabled: true,
    };
  }

  if(helper.isDefined(lcaps.browserName) && lcaps.browserName.toLowerCase() === 'edge' && parseFloat(lcaps["browser_version"] || lcaps["version"]) >= 118){
    newPrefs.third_party_search = {
      consented: true
    };
  }

  lcaps.chromeOptions.prefs = newPrefs;
};

function removeChromeOptionsDownloadLocation(caps) {
  // Ignoring manually changed download directory
  constants.CHROME_OPTIONS_PREFERENCE.forEach((option) => {
    if (caps[option] && caps[option]['prefs']) {
      delete caps[option]['prefs']['download.default_directory'];
      if (caps[option]['prefs']['download']) {
        delete caps[option]['prefs']['download']['default_directory'];
      }
    }
    if (caps["W3C_capabilities"] && caps["W3C_capabilities"]["firstMatch"]) {
      if (caps["W3C_capabilities"]["firstMatch"][0][option] && caps["W3C_capabilities"]["firstMatch"][0][option]['prefs']) {
        delete caps["W3C_capabilities"]["firstMatch"][0][option]['prefs']['download.default_directory'];
        if (caps["W3C_capabilities"]["firstMatch"][0][option]['prefs']['download']) {
          delete caps["W3C_capabilities"]["firstMatch"][0][option]['prefs']['download']['default_directory'];
        }
      }
    }
  });
}

// for both chrome and and edge chromium
var fixChrome = function(lcaps, browserstackParams) {
  if(lcaps["chromeOptions"]) {
    var version = parseFloat(lcaps["version"]);
    if(version > 28) {
      chromeOptionsPrefs(lcaps);

      if(version > 34) {
        //As per chrome driver documentation, args is always a list of strings
        lcaps['chromeOptions']['args'] = lcaps['chromeOptions']['args'] || [];
        lcaps['chromeOptions']['args'] = typeof(lcaps['chromeOptions']['args']) == "string" ? [lcaps['chromeOptions']['args']] : lcaps['chromeOptions']['args'];
        try {
          lcaps["chromeOptions"]["args"].push('test-type');
          if(isUndefined(browserstackParams["browserstack.noPipeline"]) || browserstackParams["browserstack.noPipeline"].toString() == "false"){
            lcaps["chromeOptions"]["args"].push('--disable-application-cache');
            lcaps["chromeOptions"]["args"].push('--media-cache-size=1');
            if (version < 72) {
              lcaps["chromeOptions"]["args"].push('--disk-cache-size=1');
            }
          }

          const isNetworkLogsEnabledSession = browserstackParams["browserstack.networkLogsV2"] === "true";
          const isLightHouseEnabledSession = browserstackParams["browserstack.performance"];
          // TODO: check impact for enabling RDP for all sessions
          if (isNetworkLogsEnabledSession || isLightHouseEnabledSession) {
            lcaps["chromeOptions"]["args"].push('--remote-debugging-port=9222');
          }

          // Add default Chrome option for version 137 and above to fix the issue of extension loading
          if (version >= 137) {
            const existingDisableFeaturesArg = lcaps["chromeOptions"]["args"].find(arg => arg.startsWith('--disable-features='));

            if (existingDisableFeaturesArg) {
              const existingFeatures = existingDisableFeaturesArg.split('=')[1].split(',');
              const newFeatures = ['DisableLoadExtensionCommandLineSwitch'];
              const combinedFeatures = [...new Set([...existingFeatures, ...newFeatures])];

              lcaps["chromeOptions"]["args"] = lcaps["chromeOptions"]["args"].filter(arg => arg !== existingDisableFeaturesArg);
              lcaps["chromeOptions"]["args"].push(`--disable-features=${combinedFeatures.join(',')}`);
            } else {
              lcaps["chromeOptions"]["args"].push('--disable-features=DisableLoadExtensionCommandLineSwitch');
            }
          }
        } catch(e) {
          HubLogger.exceptionLogger("WTF!! User passing invalid chrome options args: " + lcaps["chromeOptions"]["args"]);
        }
        if (version > 41) {
          if (lcaps['chromeOptions']['perfLoggingPrefs'] && lcaps['chromeOptions']['perfLoggingPrefs']['enableTimeline']) {
            if (lcaps['chromeOptions']['perfLoggingPrefs']['enableTimeline'] == true){
              delete lcaps['chromeOptions']['perfLoggingPrefs']['enableTimeline'];
              lcaps['chromeOptions']['perfLoggingPrefs']['traceCategories'] = 'blink.console,disabled-by-default-devtools.timeline';
            }
          }
        }

        if (version >= 79 &&  isNotUndefined(lcaps["acceptSslCerts"]) && lcaps["acceptSslCerts"].toString() == "true") {
          lcaps["chromeOptions"]["args"].push('--ignore-certificate-errors');
        }

        if (version >= 132 && isNotUndefined(lcaps["chromeOptions"]["args"]) && Array.isArray(lcaps["chromeOptions"]["args"])) {
          lcaps["chromeOptions"]["args"] = lcaps["chromeOptions"]["args"].filter((arg) => {
            return arg && arg.trim() !== '--headless=old';
          });
        }
      }
    }
    if(version < 36) {
      if (lcaps['chromeOptions']['perfLoggingPrefs'])
        delete lcaps['chromeOptions']['perfLoggingPrefs'];
    }

    if(version >= 22 && version <= 25 && lcaps['platform'] && lcaps['platform'].toLowerCase() == "windows"){
      lcaps['chromeOptions']['args'] = lcaps['chromeOptions']['args'] || [];
      lcaps['chromeOptions']['args'] = typeof(lcaps['chromeOptions']['args']) == "string" ? [lcaps['chromeOptions']['args']] : lcaps['chromeOptions']['args'];
      lcaps["chromeOptions"]["args"].push('user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data ' + version);
    }
  }
};

//Disable sideBar by default on Edge 106 onwards for OS X. If customer has passed an explicit capability to enable it, don't disable.
var fixEdge = function(lcaps, browserstackParams) {
  if(lcaps["chromeOptions"] && ((helper.isDefined(lcaps.browserName) && lcaps.browserName.toLowerCase() === 'edge') || (helper.isDefined(lcaps.browser) && lcaps.browser.toLowerCase() === 'edge')) && parseFloat(lcaps["version"]) >= 106) {
    if(isNotUndefined(browserstackParams["browserstack.edge.enableSidebar"]) && browserstackParams["browserstack.edge.enableSidebar"].toString() == "true"){
      return;
    }
    if (lcaps["orig_os"].match(/mac/)) {
      //As per chrome driver documentation, args is always a list of strings
      lcaps['chromeOptions']['args'] = lcaps['chromeOptions']['args'] || [];
      lcaps['chromeOptions']['args'] = typeof(lcaps['chromeOptions']['args']) == "string" ? [lcaps['chromeOptions']['args']] : lcaps['chromeOptions']['args'];
      try {
        lcaps["chromeOptions"]["args"].push('--disable-features=msHubApps');
      } catch(e) {
        HubLogger.exceptionLogger("Encountered invalid chrome options args: " + lcaps["chromeOptions"]["args"]);
      }
    }
    // Add disable-sync for edge crash happening after 119 beta - https://techcommunity.microsoft.com/t5/discussions/urgent-edge-canary-just-crashes-at-least-2-min-after-opening/m-p/3963623/highlight/false#M61087
    if (parseFloat(lcaps["version"]) >= 119) {
      lcaps['chromeOptions']['excludeSwitches'] = ["disable-sync"];
    }
    lcaps.chromeOptions.prefs.browser = { ...lcaps.chromeOptions.prefs.browser, show_hub_apps_tower: false, show_hub_apps_tower_pinned: false };
  }
};

async function fireCommands(options, callback, attempt) {
  var get_url = "",
      port_no = options.bsCaps["orig_os"].indexOf("win") > -1? "4567" : "45671",
      fireCommandTimeout = constants.terminal_cmd_exec_max_time,
      getOptions = {};
  attempt = attempt || 0;
  helper.addToConsoleTimes(options.request, `fire-command`);
  if ( isNotUndefined(options.bsCaps["resolution"]) ){
    options.request.resolution = options.bsCaps["resolution"];
    var resolution_array = options.bsCaps["resolution"]? options.bsCaps["resolution"].split("x") : [];
    if(resolution_array.length === 2){
      var width = (resolution_array[0] || constants.DEFAULT_RESOLUTION_WIDTH).trim();
      var height = (resolution_array[1] || constants.DEFAULT_RESOLUTION_HEIGHT).trim();
      if(!(width == constants.DEFAULT_RESOLUTION_WIDTH && height == constants.DEFAULT_RESOLUTION_HEIGHT)) {
        getOptions.resw = width;
        getOptions.resh = height;
        getOptions.colorbit = 32;
      }
    }
  }

  if(options.browserstackParams["mediaFiles"]) {
    getOptions.custom_media = JSON.stringify(options.browserstackParams["mediaFiles"]);
  }
  if(options.browserstackParams["safariPluginFiles"]) {
    getOptions.safari_plugin = JSON.stringify(options.browserstackParams["safariPluginFiles"]);
  }

  if (isTrueString(options.browserstackParams['browserstack.browserProfiling'])) {
    getOptions.browser_profiling = true;
    getOptions.browser_profiling_freq = constants.BROWSER_PROFILING_FREQ;
  }

  if (isTrueString(options.browserstackParams['browserstack.geoComplyApp'])) {
    getOptions.geo_comply_app = true;
    fireCommandTimeout = constants.GEOCOMPLY_FIRECMD_TIMEOUT; // Updating firecmd timeout to 75 seconds for geocomply sessions
  }

  if (options.browserstackParams.extended_idle_timeout &&
      options.browserstackParams.extended_idle_timeout > 0) {
    getOptions.extended_idle_timeout = options.browserstackParams.extended_idle_timeout;
  }

  if (options.post_params.framework === 'puppeteer') {
    getOptions.isPuppeteer = true;
    getOptions.start_cdp_proxy = true;
    getOptions.cdp_proxy_port = REMOTE_DEBUGGER_PORT;
    getOptions.cdp_upstream = 'ws://localhost:9222';
  } else if (options.post_params.framework === 'playwright') {
    const { desiredCapabilities: postJSON } = JSON.parse(options.post_data);
    // Ignore the inconsistency of camelCase and snake_case since
    // servers in different languages and standards.
    getOptions.is_playwright = true;
    getOptions.playwright_port = REMOTE_DEBUGGER_PORT;
    getOptions.cdp_proxy_port = REMOTE_DEBUGGER_PORT;
    getOptions.start_cdp_proxy = true;
    getOptions.cdp_upstream = 'ws://localhost:9222';
    getOptions.cdp_custom_request_enabled = options.browserstackParams["browserstack.cdpProxyCustomRequestEnabled"];
    const {
      playwrightVersion,
      browser,
      args,
      ignoreDefaultArgs,
      useBundledBrowser,
      firefoxUserPrefs,
      launchPersistentContext,
    } = extractPlaywrightConfig(options.browserstackParams, postJSON);
    getOptions.playwright_version = playwrightVersion;
    getOptions.browser = browser;
    getOptions.launch_persistent_context = launchPersistentContext;
    if (isNotUndefined(args)) {
      getOptions.playwright_args = encodeURIComponent(JSON.stringify(args));
    }
    if (isNotUndefined(ignoreDefaultArgs)) {
      getOptions.playwright_ignore_default_args = encodeURIComponent(JSON.stringify(ignoreDefaultArgs));
    }
    if (isNotUndefined(useBundledBrowser)) {
      getOptions.use_bundled_browser = true;
    }
    if (isNotUndefined(firefoxUserPrefs)){
      getOptions.playwright_firefox_prefs = encodeURIComponent(JSON.stringify(firefoxUserPrefs));
    }
  }

  // Capability to capture cookie in network logs
  if (isTrueString(options.bsCaps['browserstack.captureCookies'])) {
    getOptions.capture_cookies = true;
  }

  // Checks for high contrast mode
  if (enableHighContrast(options)) {
    getOptions.high_contrast = true;
  }

  if( isNotUndefined(options.browserstackParams["browserstack.gpsLocation"]) ) {
    var lat_long = options.browserstackParams['browserstack.gpsLocation'].split(",");
    getOptions.latitude = parseFloat(lat_long[0].trim());
    getOptions.longitude = parseFloat(lat_long[1].trim());
  }

  if (isTrueString(options.browserstackParams["browserstack.telemetryLogs"])) {
    getOptions.telemetryLogs = true;
  }


  if (isNotUndefined(options.browserstackParams["extraAccess"])) {
    getOptions.extraAccess = options.browserstackParams["extraAccess"];
  }

  if (isNotUndefined(options.browserstackParams["skip_platform_enterprise_flow"])) {
    getOptions.skip_platform_enterprise_flow = options.browserstackParams["skip_platform_enterprise_flow"];
  }

  constants.BRIDGECLOUD_PARAMS.forEach((bridgeCloudParam) => {
    if (isNotUndefined(options.browserstackParams[`browserstack.${bridgeCloudParam}`])) {
      getOptions[bridgeCloudParam] = options.browserstackParams[`browserstack.${bridgeCloudParam}`];
    }
  });

  if (options.browserstackParams["browserstack.video"] == "true") {
    getOptions.video = true;
    getOptions.video_aws_keys = options.browserstackParams['browserstack.video.aws.key'];
    getOptions.video_aws_secret = options.browserstackParams['browserstack.video.aws.secret'];
    getOptions.video_aws_bucket = options.browserstackParams['browserstack.video.aws.s3bucket'];
    getOptions.video_aws_region = options.browserstackParams['browserstack.video.aws.region'];
    getOptions.video_file = options.browserstackParams['browserstack.video.filename'] || "video";
    getOptions.video_disable_watermark = options.browserstackParams['browserstack.video.disableWaterMark'] || "false";

    var deviceName;
    if (options.realMobile) {
      deviceName = options.bsCaps["mobile"]["version"].split("-")[0];
      if (deviceName != null) {
        // default width changed to 800 since in landscape mode, the previous width of 400 resulted in
        // loss of details & blurred video. This was because the width 400 caused the size to reduce while
        // maintaining the ratio. Custom height/width for any device can be added in config `androidVideoResolution` in the following manner:
        // "device": {"width": "<WIDTH>", "height": "<HEIGHT>"}
        // iOS doesn't use these params. It's only for Android.
        let deviceVideoSize = constants.ANDROID_VIDEO_RESOLUTION[deviceName];
        let [height, width] = deviceVideoSize ? [deviceVideoSize["height"], deviceVideoSize["width"]] : [constants.DEFAULT_ANDROID_VIDEO_HEIGHT, constants.DEFAULT_ANDROID_VIDEO_WIDTH];
        getOptions.video_height = height;
        getOptions.video_width = width;
      }
    }
  }

  // exclude automate android sessions
  const isAndroidSession = options.bsCaps.mobile && ([options.bsCaps.platform || '', options.bsCaps["orig_os"]].some(x => x.toLowerCase() === 'android') || [options.browserName || ''].some(x => ['chrome_android', 'samsung', 'android'].includes(x.toLowerCase())));
  const isAutomateAndoridSession = isUndefined(options.bsCaps.app) && isAndroidSession;
  if (options.browserstackParams["network_simulation"] == "true" && !isAutomateAndoridSession) {
    getOptions.networkSimulation = true;
    getOptions.network_bw_dwld = options.browserstackParams['network_bw_dwld'];
    getOptions.network_bw_upld = options.browserstackParams['network_bw_upld'];
    getOptions.network_latency = options.browserstackParams['network_latency'];
    getOptions.network_pk_loss = options.browserstackParams['network_pk_loss'];
    getOptions.network_wifi = options.browserstackParams['network_wifi'];
    getOptions.network_airplane_mode = options.browserstackParams['network_airplane_mode'];
  }

  if (options.browserstackParams["browserstack.bfcache"] == "0") {
    getOptions.bfcache = 0;
  }

  // use mitmproxy for networkLogs in iOS NJB, all App-Automate Appium sessions, Samsung browser Automate sessions and in Playwright-Android sessions.
  // use chrome-har-capturer for networkLogs for all Chrome Android Automate sessions.
  if((options.bsCaps["device"] && options.bsCaps["device"].match(/iphone|ipad|appletv/i)) ||  isNotUndefined(options.bsCaps["app"]) ) {
    if(isTrueString(options.browserstackParams["browserstack.networkLogs"])) {
      getOptions.networkLogs = true;
    }

    if(isTrueString(options.browserstackParams["browserstack.acceptInsecureCerts"])) {
      getOptions.acceptInsecureCerts = true;
    }
  // else if condition to allow networkLogs for samsung browser or playwright chrome android sessions
  } else if (options.bsCaps.mobile && options.bsCaps["browserName"] &&
    ((options.bsCaps["browserName"].toLowerCase() === "samsung") || (options.post_params.framework === 'playwright' && options.bsCaps["browserName"].toLowerCase() === "chrome_android")) &&
    options.bsCaps["platform"] && options.bsCaps["platform"].toLowerCase() === "android" ) {
    if(isTrueString(options.browserstackParams["browserstack.networkLogs"])) {
      getOptions.networkLogs = true;
    }

    if(isTrueString(options.bsCaps["acceptSslCert"]) || isTrueString(options.bsCaps["acceptSslCerts"]) || isTrueString(options.bsCaps["acceptInsecureCerts"])) {
      getOptions.samsungAcceptSslCert = true;
    }

    if(isTrueString(options.browserstackParams["browserstack.acceptInsecureCerts"]) && options.post_params.framework === 'playwright') {
      getOptions.acceptInsecureCerts = true;
    }
  } else {
    // Enable browsermob for dekstop networkLogs sessions.
    if (isUndefined(options.bsCaps["device"]) && options.browserstackParams["browserstack.networkLogs"] === "true") {
      getOptions.browsermob = true;
      const networkLogsOptions = (options.browserstackParams["browserstack.networkLogsOptions"] || '');
      if(networkLogsOptions){
        getOptions.captureContent = helper.nestedKeyValueGeneric(networkLogsOptions, ['captureContent'], false, isTrueString);
      }
      if(attempt > 0 && !options.bsCaps["orig_os"].match(/mac/i)) {
        HubLogger.miscLogger('NetworkLogs:', "Allowing NetworkLogs to be disabled in case it fails in firecmd", LL.WARN);
        getOptions.ignoreBrowserMobError = true;
      }
    }

    if (isTrueString(options.browserstackParams['browserstack.enableAudioInjection']) && isNotUndefined(options.browserstackParams['browserstack.audioInjectionURL'])) {
      getOptions.enableAudioInjection = options.browserstackParams['browserstack.enableAudioInjection'].toString().toLowerCase();
      getOptions.audioInjectionURL = options.browserstackParams['browserstack.audioInjectionURL'].toString();
    }


    if (isTrueString(options.browserstackParams['browserstack.cameraInjection']) && isNotUndefined(options.browserstackParams['browserstack.cameraInjectionUrl'])) {
      getOptions.cameraInjection = options.browserstackParams['browserstack.cameraInjection'].toString()
        .toLowerCase();
      getOptions.cameraInjectionUrl = options.browserstackParams['browserstack.cameraInjectionUrl'].toString();
    }
    // Enable chrome har capturer for v2
    if (options.browserstackParams["browserstack.networkLogsV2"] === "true") {
      getOptions.chrome_har_capturer = true;
    }
  }

  if (isTrueString(options.browserstackParams['browserstack.enableTransparentMode'])) {
    getOptions.enableTransparentMode = true;
  }

  if (isTrueString(options.browserstackParams['browserstack.networkLogsPatch'])) {
    getOptions.networkLogsPatch = true;
  }

  if (options.has_edge_extension === true && options.bsCaps["orig_os"].includes("win")) {
    if(typeof options.browserstackParams["browserstack.selenium.jar.version"] == "string" && helper.isVersionEqualOrGreater("4.0.0", options.browserstackParams["browserstack.selenium.jar.version"])) {
      getOptions.disableEdgedriverLogs = false;
    } else {
      getOptions.disableEdgedriverLogs = true;
      getOptions.forceChangeJar = true;
      options.shouldForceChangeJar = true; // need to send to rails in reg session for selenium logs to work
    }
  }

  if (isTrueString(options.bsCaps.safariAllowPopups)) {
    getOptions.safariAllowPopups = true;
  }

  if(options.bsCaps["browserName"] === "firefox" && options.browserstackParams["browserstack.console"]) {
    getOptions.launchFoxDriverServer = true;
  }

  if (isFalseString(options.browserstackParams["browserstack.preventCrossSiteTracking"])) {
    getOptions.preventCrossSiteTracking = true;
  }

  if (options.bsCaps["browserName"] === "safari" && (options.bsCaps["orig_os"] === "macelc" || options.bsCaps["orig_os"] === "macyos" || options.bsCaps["orig_os"] === "macml" || options.bsCaps["orig_os"] === "macmav")){
    getOptions.unlockkeychain = 1;
  }
  if (options.bsCaps["browserName"] === "safari" &&  isNotUndefined(options.browserstackParams["browserstack.safari.enablePopups"]) && options.browserstackParams["browserstack.safari.enablePopups"].toString() == "true") {
    getOptions.enablePopupsSafari = true;
  }
  if (options.bsCaps["browserName"] === "safari" &&  isNotUndefined(options.browserstackParams["browserstack.safari.allowAllCookies"]) && options.browserstackParams["browserstack.safari.allowAllCookies"].toString() == "true") {
    getOptions.allowAllCookies = true;
  }
  if (options.bsCaps["browserName"] === "safari" &&  isNotUndefined(options.browserstackParams["browserstack.safari.disableAutoOpenSafeDownloads"]) && options.browserstackParams["browserstack.safari.disableAutoOpenSafeDownloads"].toString() == "true") {
    getOptions.disableAutoOpenSafeDownloads = true;
  }
  // Accept SSl implementation - https://browserstack.atlassian.net/wiki/spaces/ENG/pages/25211299/Automate+testing+with+Ssl+Certs+Self+Signed+Certificates
  // Update: We will need to run UI automation script for OS monterey and above. https://browserstack.atlassian.net/browse/APS-5679
  if (options.bsCaps["browserName"] === "safari" &&  isNotUndefined(options.bsCaps["acceptSslCerts"]) && options.bsCaps["acceptSslCerts"].toString() == "true" && !['machs', 'macmo', 'maccat', 'macbsr'].includes(options.bsCaps['orig_os'])) {
    getOptions.enableCertSafari = true;
  }
  if (options.bsCaps["browserName"] === "safari" && options.bsCaps["safari.options"] && options.bsCaps["safari.options"]["technologyPreview"] && options.bsCaps["safari.options"]["technologyPreview"].toString() === 'true') {
    getOptions.launch_safari_tech_preview_accept_script = true;
  }
  if (options.bsCaps["browserName"] === "safari" &&  isNotUndefined(options.browserstackParams["browserstack.disableCorsRestrictions"]) && options.browserstackParams["browserstack.disableCorsRestrictions"].toString() == "true") {
    getOptions.disableCorsRestrictions = true;
  }
  if (options.browserName == "IE" || options.browserName == "INTERNET EXPLORER"){
    getOptions.maxConnection = 10;
  }
  if ((options.browserName == "IE" || options.browserName == "INTERNET EXPLORER" || isEdgeButNonChromiumBased(options.browserName, options.bsCaps["version"])) && isNotUndefined(options.bsCaps["acceptSslCerts"]) && options.bsCaps["acceptSslCerts"].toString() == "true" && isUndefined(options.bsCaps["browserstack.edgeVersion"])) {
    getOptions.acceptSslCertsIE = true;
  }
  if((options.browserName == "EDGE" || options.browserName == "MICROSOFTEDGE") && isNotUndefined(options.browserstackParams["browserstack.edge.enablePasswordManager"])  && options.browserstackParams["browserstack.edge.enablePasswordManager"].toString().toLowerCase() == "true") {
    getOptions.enablePasswordManager = true;
  }
  if((options.browserName == "EDGE" || options.browserName == "MICROSOFTEDGE") && isNotUndefined(options.browserstackParams["browserstack.edge.proxy"])) {
    getOptions.customEdgeProxy = options.browserstackParams["browserstack.edge.proxy"];
  }
  if((/edge/i).test(options.browserName) && isNotUndefined(options.browserstackParams["browserstack.enableFlash"])) {
    getOptions.enableFlashEdge = options.browserstackParams["browserstack.enableFlash"];
  }
  if (isNotUndefined(options.browserstackParams["browserstack.ie.noFlash"]) && (options.browserName == "IE" || options.browserName == "INTERNET EXPLORER") && options.browserstackParams["browserstack.ie.noFlash"].toString() == "true") {
    getOptions.disableFlashIE = true;
  }
  if (isNotUndefined(options.browserstackParams["browserstack.ie.disableContentView"]) && (options.browserName && ( options.browserName.toLowerCase() == "ie" || options.browserName.toLowerCase() == "internet explorer" )) && options.browserstackParams["browserstack.ie.disableContentView"].toString() == "true") {
    getOptions.ieDisableReadingView = true;
  }
  if (typeof options.browserstackParams["browserstack.selenium.jar.version"] == "string"){
    getOptions.seleniumVersion = options.browserstackParams["browserstack.selenium.jar.version"];
    if (isTrueString(options.browserstackParams['browserstack.seleniumCdp'])) {
      if (helper.validateCdpSeleniumJar(getOptions.seleniumVersion)) {
        getOptions.start_cdp_proxy = true;
        // Set the upstream address for CDP proxy to be 5555, which is the port on which non launch-once Selenium Jar will run during FireCMD.
        getOptions.cdp_upstream = 'ws://localhost:5555';
      } else {
        delete options.browserstackParams['browserstack.seleniumCdp'];
      }
    }
    if (isTrueString(options.browserstackParams['browserstack.seleniumBidi'])) {
      if (helper.validateBidiSeleniumJar(getOptions.seleniumVersion)) {
        getOptions.start_cdp_proxy = true;
        // Set the upstream address for CDP proxy to be 5555, which is the port on which non launch-once Selenium Jar will run during FireCMD.
        getOptions.cdp_upstream = 'ws://localhost:5555';
      } else {
        delete options.browserstackParams['browserstack.seleniumBidi'];
      }
    }
    // use mapping for safari driver
    // Not setting safaridriver for Sierra as it is packaged with OS
    if (isUndefined(options.browserstackParams["browserstack.safari.driver"]) && options.bsCaps["browserName"] === "safari" && !['macsie', 'machs', 'macmo', 'maccat', 'macbsr', 'macmty', 'macven', 'macson', 'macsqa'].includes(options.bsCaps['orig_os'])) {
      if (options.bsCaps["orig_os"] == "macelc") {
        getOptions.safariDriverVersion = "2.45";
        fireCommandTimeout = 40000;
      }
      else {
        getOptions.copySafariDriverVersion = "2.45";
      }
    }
  }
  if (options.browserName == "IE" || options.browserName == "INTERNET EXPLORER") {
    getOptions.ieDriver = options.browserstackParams["browserstack.ie.driver"] || "2.46";

    if (options.browserstackParams["browserstack.ie.arch"]) {
      getOptions.ieDriverArch = options.browserstackParams["browserstack.ie.arch"];
    }
  }
  if (typeof options.browserstackParams["browserstack.timezone"] == 'string') {
    if (options.realMobile) { // Since validations are already performed on rails
      getOptions.timezone = options.browserstackParams["browserstack.timezone"];
      if (options.browserstackParams["browserstack.timezone_mdm"]) {
        getOptions.timezone_mdm = options.browserstackParams["browserstack.timezone_mdm"];
      }
    } else if (constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]]) {
      if (options.bsCaps["orig_os"].match(/mac/i))
        getOptions.timezone = constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]][0];
      else {
        if (constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]][1] != null)
          getOptions.timezone = constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]][1];
      }
    }
  }
  if ( isNotUndefined(options.browserstackParams["browserstack.ie.enablePopups"])  && (options.browserName == "IE" || options.browserName == "INTERNET EXPLORER") && options.browserstackParams["browserstack.ie.enablePopups"].toString() == "true") {
    getOptions.enablePopupsIE = true;
  }

  if ( isNotUndefined(options.browserstackParams["browserstack.edge.enablePopups"]) && (options.browserName == "EDGE" || options.browserName == "MICROSOFTEDGE") && options.browserstackParams["browserstack.edge.enablePopups"].toString() == "true") {
    getOptions.enablePopupsEdge = true;
  }

  if (options.bsCaps["orig_os"].includes("win") && isNotUndefined(options.browserstackParams["browserstack.edge.enableSidebar"]) && (options.browserName == "EDGE" || options.browserName == "MICROSOFTEDGE") && options.browserstackParams["browserstack.edge.enableSidebar"].toString() == "true") {
    getOptions.enableSidebarEdge = true;
  }

  if ( isNotUndefined(options.bsCaps["ie.forceCreateProcessApi"])  && options.bsCaps["ie.forceCreateProcessApi"].toString() == "true") {
    getOptions.tabProcGrowth = 0;
  }
  if (options && options.user_id && (options.browserName == "IE" || options.browserName == "INTERNET EXPLORER") && [ 2306147 ].indexOf(options.user_id) > -1) {
    getOptions.tabProcGrowth = 0;
  }
  if (typeof options.browserstackParams["browserstack.hosts"] === 'string') {
    getOptions.new_hosts = options.browserstackParams["browserstack.hosts"];
  }
  if (typeof options.browserstackParams["browserstack.safari.driver"] == "string" && options.bsCaps["browserName"] == "safari") {
    if (options.bsCaps["orig_os"] == "macelc") {
      getOptions.safariDriverVersion = options.browserstackParams["browserstack.safari.driver"];
      fireCommandTimeout = 40000;
    } else {
      getOptions.copySafariDriverVersion = options.browserstackParams["browserstack.safari.driver"];
    }
    if (typeof options.browserstackParams["browserstack.selenium.jar.version"] != "string" && options.bsCaps["orig_os"] != "macsl") {
      getOptions.seleniumVersion = constants.selenium_version_for_safari_driver[options.browserstackParams["browserstack.safari.driver"]];
    }
  }
  if(options.bsCaps["browserName"] == "safari"  && typeof options.browserstackParams["browserstack.selenium.jar.version"] != "string" && typeof options.browserstackParams["browserstack.safari.driver"] != "string") {
      var safariDriverVersion = ( isUndefined(options.bsCaps["count"])) ? constants.default_safaridriver : constants.protractor_safaridriver;
      if (options.bsCaps["orig_os"] == "macelc") {
        getOptions.safariDriverVersion = safariDriverVersion;
        fireCommandTimeout = 40000;
      } else {
        getOptions.copySafariDriverVersion = safariDriverVersion;
      }
      if(options.bsCaps["orig_os"] != "macsl") {
        getOptions.seleniumVersion = constants.selenium_version_for_safari_driver[safariDriverVersion];
      }
  }
  if (options.bsCaps["browserName"] == "firefox" && options.bsCaps["version"] >= 43.0 && isNotUndefined(options.bsCaps["marionette"])  && options.bsCaps["marionette"].toString().toLowerCase() === "true") {
    if (options.bsCaps["orig_os"].match(/mac/)) {
      getOptions.marionette = true;
      getOptions.marionette_firefox_version = parseInt(options.bsCaps['browser_version']).toString() + ".0";
    } else {
      var browser = options.bsCaps["browser"].toLowerCase();
      for (var i=0; i < constants.browserConfig[browser].length; i++) {
        if (constants.browserConfig[browser][i].version === options.bsCaps["version"] && constants.browserConfig[browser][i].release === "stable") {
          var browser_path = constants.browserConfig[browser][i]["command"].split("\"")[3].trim();
          if (options.bsCaps["64bit"] && options.bsCaps["64bit"].toString() == "true") {
            browser_path = browser_path.replace("Program Files", "Program Files (x86)");
          }
          getOptions.marionetteBrowserVersion = browser_path.replace("\\firefox.exe", "");
          break;
        }
      }
    }
    if (isNotUndefined(options.browserstackParams["browserstack.marionette.driverVersion"])) {
      var marionetteDriverVersion = options.browserstackParams["browserstack.marionette.driverVersion"];
      if(marionetteDriverVersion === '0.6.2') {
        getOptions.marionetteDriverVersion = marionetteDriverVersion;
      }
    }
  }

  if (isTrueString(options.browserstackParams['browserstack.webrtcStreaming'])) {
    getOptions.peer_server_url = options.browserstackParams['browserstack.peerServerUrl'];
    getOptions.ice_servers = options.browserstackParams['browserstack.iceServers'];
    getOptions.webrtc_session_id = options.browserstackParams['browserstack.webrtcSessionId'];
    getOptions.use_replay_kit_for_interaction = options.browserstackParams['browserstack.useReplayKitForInteraction'];
    getOptions.use_rtc_app = options.browserstackParams['browserstack.useRtcApp'];
    getOptions.use_rtc_app_audio = options.browserstackParams['browserstack.useRtcAppAudio'];
    getOptions.use_interactive_session_refresh_flow = options.browserstackParams['browserstack.useInteractiveSessionRefreshFlow'];
  }

  if (isNotUndefined(options.browserstackParams['browserstack.video_v2_enabled'])) {
    getOptions.videoV2Enabled = options.browserstackParams['browserstack.video_v2_enabled'];
  }

  if(isNotUndefined(options.browserstackParams["browserstack.chrome.driver"])) {
    getOptions.chromeDriverVersion = options.browserstackParams["browserstack.chrome.driver"];
  }

  if (isNotUndefined(options.browserstackParams["browserstack.edge.driver"])) {
    getOptions.edgeDriverVersion = options.browserstackParams["browserstack.edge.driver"];
    delete getOptions.chromeDriverVersion;
  }

  if(options.browserstackParams["browserstack.geckodriver"]) {
    getOptions.geckoDriverVersion = options.browserstackParams["browserstack.geckodriver"];
    fireCommandTimeout += 10000;
  }

  if(!options.bsCaps["device"] && !options.browserName.match(/iphone|ipad|android|appletv/i)) {
    if(getOptions.seleniumVersion == null) {
      options.port = constants.default_seleniumjar_port;
    }
    if(options.browserstackParams["browserstack.selenium.jar.version"]) {
      options.request.selenium_version = options.browserstackParams["browserstack.selenium.jar.version"];
    }
    options.request.selenium_version = options.browserstackParams["browserstack.selenium.jar.version"] || getOptions.seleniumVersion;
    options.bsCaps["selenium_version"] = options.request.selenium_version;

    var eligibleForDirectChromeDriver = options.bsCaps["browserName"] == "chrome" && (!options.bsCaps["orig_os"].match(/mac/i));
    if (eligibleForDirectChromeDriver) {
      var chromeDriverArray = constants.chromeDriverNodes;
      for(var eachIndex in chromeDriverArray) {
        var anyHash = chromeDriverArray[eachIndex];
        if(anyHash["version"] === getOptions.chromeDriverVersion) {
          options.port = anyHash["port"];
          delete getOptions.chromeDriverVersion;
          break;
        }
      }
    }
  }
  // Log the previous session id in case this is a retry. As the this is a new session, the session ID will be different.
  // Usually, we grep with the session ID present on the dashboard. As the previous session is deleted (the one which failed), we cannot see the actual reason for the failure of the previous session.
  // This log line will give us the session id of the previous run.
  const isAppAutomate = options.post_params ? options.post_params.isAppAutomate : false;
  if (options.prev_session_id) {
    HubLogger.miscLogger("DIFF-SESSION-RETRY", "Old-Session ID: " + options.prev_session_id + " New-Session ID: " + options.sessionId, LL.INFO);
    var kind = options.bsCaps["app"] ? "app_diff_session_retry" : "diff_session_retry";
    helper.PingZombie({
      "timestamp": (Math.round(new Date().getTime() / 1000).toString()),
      "session_id": options.sessionId,
      "data": options.prev_session_id,
      "kind": kind,
      "browser": options.post_params.framework,
      "user_id": options.user_id,
      "os": options.bsCaps["platform"] || options.bsCaps["os"]
    });
    helper.pushToCLS(kind, {
      session_id: options.sessionId,
      user_id: options.user_id,
      old_session: options.prev_session_id
    }, isAppAutomate);
  }
  if (!options.bsCaps["setSocksProxy"] && (options.browserstackParams["browserstack.tunnel"] == "true" || options.browserstackParams["browserstack.tunnel"] == true)) {
    getOptions.local = true;
    if (/rproxy/.test(options.rproxyHost)) {
      getOptions.auth = true;
    }

    if (isNotUndefined(options.browserstackParams["local_params"])) {
      fireCommandTimeout += 30000;
      var local_params = options.browserstackParams["local_params"];
      for(var j in local_params){
        getOptions[j] = local_params[j];
      }
    }

    const { group_id: groupId, user_id: userId } = options;
    await configurePrivoxyTimeout(getOptions, {userId, groupId});
  }
  if (options.browserstackParams["browserstack.ie.compatibility"]) {
    if(constants.ie_compatibility_modes.indexOf(parseInt(options.browserstackParams["browserstack.ie.compatibility"])) > -1) {
      getOptions.ie_compatibility = options.browserstackParams["browserstack.ie.compatibility"];
    } else {
      HubLogger.miscLogger("IECompatibility", options.sessionId + " Unrecognized IE Compatibility Value: " + options.browserstackParams["browserstack.ie.compatibility"], LL.INFO);
    }
  }
  // Appium specific
  if (options.bsCaps["platform"] && options.bsCaps["platform"].toLowerCase() == "mac" && (options.browserName.toLowerCase() == "iphone" || options.browserName.toLowerCase() == "ipad" || options.browserName.toLowerCase() == "appletv" || options.browserName.toLowerCase().match(/chromium_(iphone|ipad)/))) {
    var mobile_params = options["mobile"];
    getOptions.appiumios = true;
    getOptions.iosVersion = options.iosVersion;
    getOptions.browser = mobile_params["browser"];
    getOptions.version = mobile_params["version"];
    fireCommandTimeout = 300000;
  } else if (options.bsCaps["mobile"] && ((options.bsCaps["platform"] && options.bsCaps["platform"].toLowerCase() === "android") || ['chrome_android', 'samsung', 'android'].includes(options.browserName.toLowerCase()) )) {
    var apiVersion = Number(options.bsCaps["mobile"]["version"].split("-")[1]);
    if (apiVersion >= 4.4) {
      deviceName = options.bsCaps["mobile"]["version"].replace(/ /g, "__");
      var deviceOrientation = (options.bsCaps["deviceOrientation"] || "portrait");
      if(typeof deviceOrientation !== "string")
        deviceOrientation = "portrait";
      if (deviceOrientation !== "portrait") {
        var tempHeight = getOptions.video_height;
        getOptions.video_height = getOptions.video_width;
        getOptions.video_width = tempHeight;
      }
      getOptions.appiumandroid = true;
      getOptions.deviceName = deviceName;
      getOptions.orientation = deviceOrientation.toLowerCase();
      getOptions.browser = options.bsCaps["mobile"]["browser"];
      getOptions.version= apiVersion;
      fireCommandTimeout = 300000;
    }
  }

  if (options.bsCaps["platform"] && options.bsCaps["platform"].toLowerCase() == "mac" && options.browserName.toLowerCase().match(/chromium_(iphone|ipad)/)) {
    getOptions.chromium_bundle_id = "true";
  }

  if (options.bsCaps["custom_headers"]) {
    getOptions.custom_headers = options.bsCaps["custom_headers"];
    // Browsermob proxy is required to be true for custom_header injection
    getOptions.browsermob = true;
    // acceptInsecureCerts is required to be true for custom_header injection
    getOptions.acceptInsecureCerts = true;
  }

  if (options.bsCaps["enable_proxy_for_insecure_websockets"]) {
    getOptions.enable_proxy_for_insecure_websockets = options.bsCaps["enable_proxy_for_insecure_websockets"];
    getOptions.insecure_ws_proxy_params = options.bsCaps["insecure_ws_proxy_params"];
  }

  if (options.browserstackParams["custom_socket_timeout"]) {
    getOptions.custom_socket_timeout = options.browserstackParams["custom_socket_timeout"];
  }

  // Real Mobile
  if (options.realMobile) {
    getOptions.device = options.bsCaps["udid"];
    getOptions.deviceName = options.bsCaps["mobile"]["version"].split("-")[0].replace(/ /g, "__");
    getOptions.genre = 'automate';
    if (options.browserName && (options.browserName.toLowerCase() == "iphone" || options.browserName.toLowerCase() == "ipad" )) {
      getOptions.iosSafariStartStop = constants.ENABLE_SAFARI_START_FLOW; // Enable Safari Start Stop Flow in firecmd
    }
    var isAppleOs = helper.isAppleOs(options.bsCaps["orig_os"]);

    if(options.browserstackParams["browserstack.reserveDevice"]) {
      getOptions.reserveDevice = options.browserstackParams["browserstack.reserveDevice"];
    }

    if(isNotUndefined(options.bsCaps["custom_replay_kit_params"])) {
      getOptions.custom_replay_kit_params = options.bsCaps["custom_replay_kit_params"];
    }

    if(isNotUndefined(options.bsCaps["non_replay_kit_params"])) {
      getOptions.non_replay_kit_params = options.bsCaps["non_replay_kit_params"];
    }

    if (isNotUndefined(options.bsCaps["media_projection_popup_coords"])) {
      getOptions.media_projection_popup_coords = options.bsCaps["media_projection_popup_coords"];
    }

    if(options.bsCaps["automationName"]) {
        getOptions.automationName = options.bsCaps["automationName"].toLowerCase();
    }

    if(isNotUndefined(options.browserstackParams["browserstack.automationVersion"])) {
      getOptions.automationVersion = options.browserstackParams["browserstack.automationVersion"];
    }

    if (isNotUndefined(options.bsCaps["app"])) {
      getOptions.appDownloadTimeout = options.browserstackParams[constants.APP_DOWNLOAD_TIMEOUT_CAP] || constants.DEFAULT_APP_DOWNLOAD_TIMEOUT;
      fireCommandTimeout = 300000 + (getOptions.appDownloadTimeout * 1000);
      getOptions.app_testing_bundle_id = options.bsCaps["bundleID"];
      getOptions.s3_app_url = options.bsCaps["app"];

      // Need to increase timeout to allow for TestFlight automation to run
      if (options.bsCaps["app"].match("testflight.apple.com")) {
        fireCommandTimeout += 150000;
      }

      if (isNotUndefined(options.bsCaps["localization"])){
        getOptions.localization = options.bsCaps["localization"];
      }
      if(options.bsCaps["otherApps"]) {
        getOptions.other_apps = options.bsCaps["otherApps"];
      }
      if(options.browserstackParams["browserstack.midSessionInstallApps"]) {
        getOptions.mid_session_install_apps = options.browserstackParams["browserstack.midSessionInstallApps"];
      }
      if(options.browserstackParams["browserstack.espressoServer"]) {
        getOptions.espresso_server_app = options.browserstackParams["browserstack.espressoServer"];
      }
      if(options.bsCaps["appClient"]) {
        getOptions.detox_app_client = options.bsCaps["appClient"];
      }
      if(options.browserstackParams["mediaFiles"]) {
        getOptions.custom_media = JSON.stringify(options.browserstackParams["mediaFiles"]);
      }
      if(options.browserstackParams["browserstack.updateIosDeviceSettings"]) {
        getOptions.updateIosDeviceSettings = options.browserstackParams["browserstack.updateIosDeviceSettings"];
      }
      getOptions.genre = 'app_automate';
      getOptions.host = constants.BS_APP_ENDPOINT;
      getOptions.deviceLogs = options.browserstackParams["browserstack.deviceLogs"];
      getOptions.setprop = options.browserstackParams["browserstack.android.setprop"];

      if(options.browserstackParams["browserstack.updateAppSettings"]) {
        getOptions.update_app_settings = options.browserstackParams["browserstack.updateAppSettings"];
        getOptions.app_display_name = options.browserstackParams["browserstack.appDisplayName"];
      }

      if(options.browserstackParams["browserstack.removeIOSAppSettingsLocalization"]) {
        getOptions.remove_ios_app_settings_localization = options.browserstackParams["browserstack.removeIOSAppSettingsLocalization"];
      }

      if(options.browserstackParams["browserstack.devicePreferences"]) {
        getOptions.device_preferences = options.browserstackParams["browserstack.devicePreferences"];
      }

      if(isNotUndefined(options.browserstackParams["browserstack.appStoreConfiguration.username"]) && isNotUndefined(options.browserstackParams["browserstack.appStoreConfiguration.password"])) {
        getOptions.app_store_username = options.browserstackParams["browserstack.appStoreConfiguration.username"];
        getOptions.app_store_password = options.browserstackParams["browserstack.appStoreConfiguration.password"];
      }

      if (isNotUndefined(options.browserstackParams["customCertificateFile"])) {
        getOptions.customCertificateFile = JSON.stringify(options.browserstackParams["customCertificateFile"]);
      }

      if(options.browserstackParams["browserstack.enablePasscode"]) {
        getOptions.enablePasscode = options.browserstackParams["browserstack.enablePasscode"].toString().toLowerCase();
      }

      if(isNotUndefined(options.bsCaps["override_privoxy_forward_rules"])) {
        getOptions.override_privoxy_forward_rules = options.bsCaps["override_privoxy_forward_rules"];
      }

      if(isNotUndefined(options.bsCaps["youiEngineAppPort"])) {
        getOptions.youiEngineAppPort = options.bsCaps["youiEngineAppPort"];
      }

      if(isNotUndefined(options.bsCaps["youiengine_driver_port"])) {
        getOptions.youiengine_driver_port = options.bsCaps["youiengine_driver_port"];
      }

      if(isNotUndefined(options.browserstackParams["browserstack.resignApp"])) {
        getOptions.resignApp = options.browserstackParams["browserstack.resignApp"];
      }

      if (isNotUndefined(options.bsCaps['app_automate_custom_params'])) {
        getOptions.app_automate_custom_params = JSON.stringify(options.bsCaps['app_automate_custom_params']);
      }

      if(options.browserstackParams["browserstack.allowDeviceMockServer"]) {
        getOptions.allow_device_mock_server = options.browserstackParams["browserstack.allowDeviceMockServer"];
      }

      if(isNotUndefined(options.bsCaps["enable_bypass_local_server_request_for_patrol"])) {
        getOptions.enable_bypass_local_server_request_for_patrol = options.bsCaps["enable_bypass_local_server_request_for_patrol"];
      }
    }

    if (options.browserstackParams['browserstack.enableAudioInjection']) {
      getOptions.enableAudioInjection = options.browserstackParams["browserstack.enableAudioInjection"].toString().toLowerCase();
    }

    if (isTrueString(options.browserstackParams['browserstack.enableCameraImageInjection'])) {
      getOptions.enableCameraImageInjection = options.browserstackParams["browserstack.enableCameraImageInjection"].toString().toLowerCase();
    }

    if (isTrueString(options.browserstackParams['browserstack.enableCameraVideoInjection'])) {
      getOptions.enableCameraVideoInjection = options.browserstackParams["browserstack.enableCameraVideoInjection"].toString().toLowerCase();
    }

    if (isTrueString(options.browserstackParams['browserstack.enableCameraPreview'])) {
      getOptions.enableCameraPreview = options.browserstackParams["browserstack.enableCameraPreview"].toString().toLowerCase();
    }

    if (isTrueString(options.browserstackParams['browserstack.enableBiometric'])) {
      getOptions.enableBiometric = options.browserstackParams["browserstack.enableBiometric"].toString().toLowerCase();
    }

    if (options.browserstackParams['browserstack.enableSim']) {
      getOptions.enableSim = options.browserstackParams['browserstack.enableSim'].toString().toLowerCase();
    }

    if(options.browserstackParams["browserstack.enableApplePay"]) {
      fireCommandTimeout += 50000; // Adding 50 seconds more in firecmd timeout in case of apple pay
      getOptions.enableApplePay = options.browserstackParams["browserstack.enableApplePay"];
    }

    if(options.browserstackParams["browserstack.applePayPreferredNetworks"]) {
      getOptions.applePayPreferredNetworks = options.browserstackParams["browserstack.applePayPreferredNetworks"];
    }
    
    if(options.browserstackParams["card_network"]) {
      getOptions.card_network = options.browserstackParams["card_network"];
    }

    if(options.browserstackParams["apple_pay_cards"]) {
      getOptions.apple_pay_cards = options.browserstackParams["apple_pay_cards"];
    }

    if(options.browserstackParams["browserstack.cameraInjection"]) {
      getOptions.cameraInjection = options.browserstackParams["browserstack.cameraInjection"];
    }

    if(options.browserstackParams["browserstack.cameraInjectionUrl"]) {
      getOptions.cameraInjectionUrl = options.browserstackParams["browserstack.cameraInjectionUrl"];
    }

    if(options.browserstackParams["browserstack.forceReinstall"]) {
      getOptions.forceReinstall = options.browserstackParams["browserstack.forceReinstall"];
    }

    if (isAppleOs) {
      if (options.bsCaps["backfill"]) {
        // Adding 120 seconds more in firecmd timeout in case of backfill
        fireCommandTimeout = fireCommandTimeout + 120000;
        getOptions.backfill = options.bsCaps["backfill"];
      }
      getOptions.keychainBioAuth = options.bsCaps["keychainBioAuth"] || false;
      getOptions.biometricUserOptionAccessible = options.bsCaps["biometricUserOptionAccessible"] || false;
    }

    if (isAppleOs && options.browserstackParams["video_params_v2"]) {
      getOptions.video_params_v2 = JSON.stringify(options.browserstackParams["video_params_v2"]);
    }

    if (options.bsCaps["disableAnimations"]) {
      getOptions.disableAnimations = options.bsCaps["disableAnimations"];
    }

    //There are cases where appium_version capability is getting converted to appiumVersion, hence supporting both.
    var isAndroid = options.bsCaps["orig_os"].toLowerCase() == "android";

    var appium_version = options.browserstackParams["browserstack.appium_version"] || options.browserstackParams["browserstack.appiumVersion"];
    getOptions.appium = appium_version;

    if (isAndroid) {
      if (options.bsCaps["inject_app"]) {
        // Adding 200 seconds more in firecmd timeout in case of app injection
        fireCommandTimeout = fireCommandTimeout + 20000;
        getOptions.inject_app = options.bsCaps["inject_app"];
      }
      getOptions.chooserIntentSupport = options.bsCaps["chooserIntentSupport"] || false;
    }

    helper.zipAlignCheck(getOptions, options);

    if(getOptions.deviceName == "Samsung__Galaxy__Tab__4"){
      if(options.bsCaps["deviceOrientation"] && options.bsCaps["deviceOrientation"] == "landscape")
        options.bsCaps["deviceOrientation"] = "portrait";
      else
        options.bsCaps["deviceOrientation"] = "landscape";
    }
    if (options.bsCaps["deviceOrientation"])
      getOptions.orientation = options.bsCaps["deviceOrientation"].toString().toLowerCase();
    if (getOptions.deviceName.match(/iphone|ipad/i)) {
      getOptions.autoAcceptAlerts = true;
      if ((isNotUndefined(options.bsCaps["acceptSslCerts"]) && options.bsCaps["acceptSslCerts"].toString() === "false") ||
            (isNotUndefined(options.bsCaps["autoAcceptAlerts"]) && options.bsCaps["autoAcceptAlerts"].toString() === "false"))
        getOptions.autoAcceptAlerts = false;
      else if (options.bsCaps["autoDismissAlerts"] && options.bsCaps["autoDismissAlerts"].toString() === "true") {
        getOptions.autoDismissAlerts = true;
        getOptions.autoAcceptAlerts = false;
      }
      if (( isNotUndefined(options.bsCaps['acceptSslCerts']) && options.bsCaps['acceptSslCerts'].toString() === 'true')) {
        getOptions.acceptSslCerts = true;
      }
    }

    helper.addToGetOptionsforAdditionalJsonCapsOnPWIOS(getOptions);

    if (options.bsCaps.paintTimingEnable && options.bsCaps.paintTimingEnable === true) {
      getOptions.paint_timing_enable = true;
    }

    getOptions.automate_session_id = options.sessionId;
    if (getOptions.appiumandroid && options.browserstackParams.local_params === undefined) {
      getOptions.proxy_type = "privoxy";
      getOptions.hosts = "*:*:50001"; // Can't be more idiotic than this but past haunts!
    }

    if (getOptions.appiumandroid && isNotUndefined(options.bsCaps['run_privoxy_as_service']) && options.bsCaps['run_privoxy_as_service'].toString() === 'true') {
      getOptions.run_privoxy_as_service = true;
    }
    let maxFireCMDtimeout = constants.MAX_FIRECMD_TIMEOUT;
    if (
      isNotUndefined(options.bsCaps["app_automate_custom_params"]) &&
      options.bsCaps["app_automate_custom_params"]["max_firecmd_timeout"] &&
      Number.isInteger(
        options.bsCaps["app_automate_custom_params"]["max_firecmd_timeout"]
      )
    ) {
      HubLogger.miscLogger(
        "fireCommands",
        options.sessionId +
          `Setting max_firecmd_timeout as ${
            options.bsCaps["app_automate_custom_params"][
              "max_firecmd_timeout"
            ] - 60 // This needs to be slightly lesser than the timeout sent from rails to avoid any unknown race conditions.
          }`,
        LL.INFO
      );
      maxFireCMDtimeout =
        options.bsCaps["app_automate_custom_params"]["max_firecmd_timeout"] -
        60;
    }
    fireCommandTimeout = Math.min(fireCommandTimeout, maxFireCMDtimeout * 1000);
    fireCommandTimeout = validateCustomMobileStartSessionTimeout(options.realMobile, options.browserstackParams["browserstack.customMobileStartSessionTimeout"], fireCommandTimeout);

    getOptions.stats_aws_key = options.browserstackParams['browserstack.stats.aws.key'];
    getOptions.stats_aws_secret = options.browserstackParams['browserstack.stats.aws.secret'];
    getOptions.stats_aws_bucket = options.browserstackParams['browserstack.stats.aws.s3bucket'];
    getOptions.stats_aws_region = options.browserstackParams['browserstack.stats.aws.region'];
    getOptions.stats_aws_storage_class = options.browserstackParams['browserstack.stats.aws.storage_class'];
  }

  if (options.browserstackParams["browserstack.networkLogsExcludeHosts"])
  {
    getOptions.exclude_host_regex = options.browserstackParams["browserstack.networkLogsExcludeHosts"];
  }
  else if (options.browserstackParams["browserstack.networkLogsIncludeHosts"])
  {
    getOptions.include_host_regex = options.browserstackParams["browserstack.networkLogsIncludeHosts"];
  }

  if (options.browserstackParams["browserstack.proxyExcludeHosts"])
  {
    getOptions.proxy_exclude_hosts = options.browserstackParams["browserstack.proxyExcludeHosts"];
  }

  /*  appProfiling = true for Mobile CSPT app profiling */
  if( isTrueString(options.browserstackParams['browserstack.appProfiling']) ||
      isTrueString(options.bsCaps['browserstack.appProfiling'])) {
    getOptions.appProfiling = true;
    getOptions.pusher_url = options.browserstackParams['pusher_url'];
    getOptions.pusher_channel = options.browserstackParams['pusher_channel'];
    getOptions.pusher_auth = options.browserstackParams['pusher_auth'];
  }
  if (isNotUndefined(options.browserstackParams["prerunFile"])) {
    getOptions.prerun = options.browserstackParams["prerunFile"];
  }

  if (Object.keys(getOptions).length !== 0) {
    getOptions.browserName = options.bsCaps['browserName'] || options.bsCaps['browser'];
    getOptions.browserVersion = options.bsCaps['browser_version'];
    getOptions.user_id = options.user_id;
    getOptions.group_id = options.group_id;
    getOptions.group_plan_type = options.group_plan_type;
    getOptions.privoxy_domain_control_flag = options.privoxy_domain_control_flag;
    getOptions.desktop_telemetry_enabled = options.desktop_telemetry_enabled;
    getOptions.desktop_telemetry_interval = options.desktop_telemetry_interval;
    getOptions.group_risk_bucket = options.group_risk_bucket;
    getOptions.bundle_id_block_flag = options.bundle_id_block_flag;
    getOptions.ios_msg_logging_flag = options.ios_msg_logging_flag;
    getOptions.terminal_ip = options.host_name;
    getOptions.logging = false;
    getOptions.logHost = constants.zombie_server;
    getOptions.session_id = options.sessionId;
    getOptions.video_session_id = options.sessionId;
    getOptions.edsKey = constants.eds_key;
    getOptions.edsHost = constants.eds_server;
    getOptions.edsPort = constants.eds_port;
    getOptions.syncTimeWithNTP = (options.browserstackParams['browserstack.syncTimeWithNTP'] || false).toString();
    getOptions.forceChangeJar = getOptions.forceChangeJar || options.browserstackParams["browserstack.forceChangeJar"];
    getOptions.xmsJar = options.browserstackParams["browserstack.xmsJar"] || '';
    getOptions.xmxJar = options.browserstackParams["browserstack.xmxJar"] || '';
    getOptions.restart_svc = (getOptions.browserName || '').toLowerCase() === 'chrome';
    getOptions.doNotSetProxy = isTrueString(options["doNotSetProxy"]);
    getOptions.genre = getOptions.genre || 'selenium';
    getOptions.clsLogHost = constants.cls_host;
    getOptions.set_custom_max_client_connections = options.browserstackParams["set_custom_max_client_connections"] || false;
    getOptions.custom_max_client_connections = options.browserstackParams["custom_max_client_connections"] || 256;
    getOptions.machine_key = options.machine_key;

    /* Add AI session details to send to terminal for browsermob */
    getOptions.ai_enabled_session = (options.browserstackParams && options.browserstackParams.realMobile && options.browserstackParams.realMobile.toString() === 'true') ? AICommandHelper.getFirecmdAIProxyOptions(options) : AICommandHelper.getFirecmdTcgOptions(options);

    helper.setAccessibilityAutomationConfigJSON(getOptions, options);

    let [currentTerminal] = (options.bsCaps['orig_os'].match(/(win|mac|android)/i) || []);

    if(typeof(currentTerminal) === 'string') {
      currentTerminal = currentTerminal.toLowerCase();
      let currentStage = constants.parallelize_stages[currentTerminal].all_users;
      if(helper.isBrowserStackGroup(options.group_id)) {
        currentStage = constants.parallelize_stages[currentTerminal].browserstack_group;
      }

      switch (currentStage) {
        case 'new_approach':
          getOptions.parallel_approach = true;
          break;
        case 'new_approach_sync':
          getOptions.parallel_approach = true;
          getOptions.sync_methods = true;
          break;
        default:
          break;
      }
    }

    var get_data = requestlib.getEncodedURLParams(getOptions);
    HubLogger.miscLogger(
      "fireCommands",
      options.sessionId + " Starting fireCommands: for " + HubLogger.filterKeys(get_data) + " on port " + options.port,
      LL.INFO
    );
    helper.pushToCLS('prepare_terminal_start', {
      session_id: options.sessionId,
      user_id: options.user,
      get_params: HubLogger.filterDetails(get_data),
      terminal: options.hostname,
      multiple_jar_port: options.port,
      timeout: fireCommandTimeout
    }, isAppAutomate);

    if (isAppAutomate) {
      helper.PingZombie({
        'kind' : 'app-firecmd-request-start',
        'timestamp': (Math.round(new Date().getTime() / 1000).toString()),
        'session_id': options.sessionId,
        'user_id': options.user,
        'region': options.browserstackParams["browserstack.terminal_region"]
      });
    }

    var get_options = {
      hostname: helper.getRproxy(options),
      port: port_no,
      path: "/selenium_command?" + get_data,
      agent: false,
      headers: {
        "Connection": "close",
      },
      timeout: fireCommandTimeout
    };

    HubLogger.miscLogger(
      "rproxy params in fireCommands",
      options.sessionId + "hostname: " + helper.getRproxy(options) +  "region :" + constants.region + "browserstackParams :" + options.browserstackParams + "options.rproxyHost :" + options.rproxyHost + "options.host_name :" + options.host_name + " on port " + options.port,
      LL.INFO
    );

    var fireCmdOptions = get_options;
    var shouldUsePost = options.browserstackParams["browserstack.enable_firecmd_post"] && options.browserstackParams["browserstack.enable_firecmd_post"].toString() === "true";
    if (shouldUsePost || constants.POST_FIRE_CMD_FRAMEWORKS.includes(options.post_params.framework)) {
      var postBody = JSON.stringify(getOptions);
      var post_options = {
        hostname: options.rproxyHost,
        method: "POST",
        port: port_no,
        path: "/selenium_command" + `?device=${getOptions['device']}&automate_session_id=${getOptions['automate_session_id']}`,
        body: postBody,
        agent: false,
        headers: {
          "Connection": "close",
          'content-type': 'application/json',
          accept: 'application/json',
          'content-length': Buffer.byteLength(postBody, 'utf-8'),
        },
        timeout: fireCommandTimeout
      };
      if (constants.POST_FIRE_CMD_FRAMEWORKS.includes(options.post_params.framework)) post_options.path = "/selenium_command";
      fireCmdOptions = post_options;
    }

    requestlib.appendBStackHostHeader(options.host_name, fireCmdOptions.headers);

    var fireCmdRequestTime = (new Date()).getTime()/1000;
    requestlib.call(fireCmdOptions)
    .then((res) => {
      var data = res.data;
      var fireCmdResponseTime = (new Date()).getTime()/1000;
      try{
        var parsed_data = JSON.parse(data);
        if(!options.bsCaps["device"] && !options.browserName.match(/iphone|ipad|android|appletv/i)) {
          options.port = parsed_data.port || options.port;
        }
        if((isUndefined(parsed_data.error) || parsed_data.error.match(/pr(iv)?oxy failed/i)) && (isUndefined(parsed_data.seleniumVersion) || (parsed_data.seleniumVersion != "failure"))) {
          HubLogger.miscLogger('fireCommands', `${options.sessionId} Response code while fireCommands: for ${get_url} ${res.statusCode} ${data}`, LL.WARN);

          if ((helper.isDefined(options.request)) && options.request.is_app_automate_session) {
            var firecmd_response_time = Math.ceil(new Date().getTime() / 1000);
            var created_at = options.request.session_created_at;
            var platform_time_stats = parsed_data["platform_time_stats"];

            var total_platform_time = 0;
            for (var key in platform_time_stats) {
              total_platform_time += platform_time_stats[key];
            }
            /**
              total_platform_time does not measure the total time spent in the platform code. It does not measure the rails time for finding device and the time in firecmd.
              Hence the remaining time which was lost otherwise is computed below as shown and is used as setting_device_time.
            **/
            var setting_device_time = Math.max(0, (firecmd_response_time - created_at - (total_platform_time / 1000)) * 1000 );
            HubLogger.sendSessionLog(options, "SESSION_SETUP_TIME", helper.getDate(), JSON.stringify({"initialising_device": setting_device_time}), false);
            HubLogger.sendSessionLog(options, "SESSION_SETUP_TIME", helper.getDate(), JSON.stringify(platform_time_stats), false);
          }

          var request_time_at_terminal = parsed_data["end_firecmd_request"] - parsed_data["start_firecmd_request"];
          var request_time_at_hub = fireCmdResponseTime - fireCmdRequestTime;
          var latency = (request_time_at_hub - request_time_at_terminal)/2;
          options["video_start_time"] = fireCmdResponseTime - latency - (parsed_data["end_firecmd_request"] - parsed_data["video_start_time"]);
          options["platform_video_start_time"] = parsed_data["video_start_time"];
          options["selenium_version"] = parsed_data.seleniumVersion;

          if (isNotUndefined(parsed_data['build_number'])) {
            options['build_number'] = parsed_data['build_number'];
          }

          if(isNotUndefined(parsed_data["udid"])){
            var tmp_data = JSON.parse(options.post_data);
            if(isNotUndefined(parsed_data["udid"]) && parsed_data["udid"].length > 0){
              if (options.bsCaps['orig_os'] == 'macelc') {
                tmp_data["desiredCapabilities"]["deviceName"] = "=" + tmp_data["desiredCapabilities"]["deviceName"].replace("6S", "6s").trim() + " (9.1) [" + parsed_data["udid"] + "]";
                tmp_data["desiredCapabilities"]["platformVersion"] = "9.1";
              } else if (options.bsCaps['orig_os'] == 'macyos') {
                tmp_data["desiredCapabilities"]["deviceName"] = "=" + tmp_data["desiredCapabilities"]["deviceName"].trim() + " (8.3 Simulator) [" + parsed_data["udid"] + "]";
              }
              options.post_data = JSON.stringify(tmp_data);
            }
          }

          var tmp_json_parse;
          if(isNotUndefined(parsed_data["wda_port"])) {
            tmp_json_parse = JSON.parse(options.post_data);
            tmp_json_parse["desiredCapabilities"]["wda_port"] = parsed_data["wda_port"];
          }

          if (isNotUndefined(parsed_data['privoxy_port'])) {
            parsed_data['proxy_port'] = parsed_data['privoxy_port'];
          }

          if (getOptions.genre !== 'automate' && isNotUndefined(parsed_data['chromedriverPorts'])) {
            tmp_json_parse = tmp_json_parse || JSON.parse(options.post_data);
            let isW3c = ("capabilities" in tmp_json_parse);
            if (isW3c) {
              tmp_json_parse["capabilities"]["firstMatch"][0]['chromedriverPorts'] = parsed_data['chromedriverPorts'];
            } else {
              tmp_json_parse["desiredCapabilities"]["chromedriverPorts"] = parsed_data['chromedriverPorts'];
            }
          }

          if(isNotUndefined(parsed_data['adb_proxy_port'])) {
            tmp_json_parse = tmp_json_parse || JSON.parse(options.post_data);
            const adbProxyArg = `--adb-port=${parsed_data['adb_proxy_port']}`;
            let isW3c = ("capabilities" in tmp_json_parse);
            let deviceVersionString = tmp_json_parse["desiredCapabilities"]["mobile"]["version"].toString();
            let apiVersion = Number(deviceVersionString.split("-")[1]);
            if (isW3c) {
              if (apiVersion < 13 && !(isNotUndefined(options.request) && options.request.is_app_automate_session)) {
                // disabling setting noReset for android 13+ devices atm, check MOBFR-906
                tmp_json_parse["capabilities"]["firstMatch"][0]["noReset"] = true;
              }
              tmp_json_parse["capabilities"]["firstMatch"][0]["chromedriverArgs"] = tmp_json_parse["capabilities"]["firstMatch"][0]["chromedriverArgs"] || [];
              if(tmp_json_parse["capabilities"]["firstMatch"][0]["chromedriverArgs"].indexOf(adbProxyArg) < 0) {
                tmp_json_parse["capabilities"]["firstMatch"][0]["chromedriverArgs"].push(adbProxyArg);
              }
            } else {
              if (apiVersion < 13 && !(isNotUndefined(options.request) && options.request.is_app_automate_session)) {
                tmp_json_parse["desiredCapabilities"]["noReset"] = true;
              }
              tmp_json_parse["desiredCapabilities"]["chromedriverArgs"] = tmp_json_parse["desiredCapabilities"]["chromedriverArgs"] || [];
              if(tmp_json_parse["desiredCapabilities"]["chromedriverArgs"].indexOf(adbProxyArg) < 0) {
                tmp_json_parse["desiredCapabilities"]["chromedriverArgs"].push(adbProxyArg);
              }
            }
          }

          if (isNotUndefined(parsed_data['proxy_port'])) {
            tmp_json_parse = tmp_json_parse || JSON.parse(options.post_data);
            ['chromeOptions', 'ms:edgeOptions'].forEach(function(option){
              var options = tmp_json_parse['desiredCapabilities'] && tmp_json_parse['desiredCapabilities'][option];
              if (options && options['args']) {
                options['args'].forEach(function(arg, index) {
                if (arg.includes('--proxy-server=http://')) {
                    options['args'][index] = arg.replace(/\d+$/, parsed_data['privoxy_port']);
                    options['args'][index] = arg.replace(/\d+$/, parsed_data['proxy_port']);
                  }
                });
              }
            });
          }
          if (isNotUndefined(parsed_data['accessibilityExtensionPath'])) {
            tmp_json_parse = tmp_json_parse || JSON.parse(options.post_data);
            const accessibilityAuthToken = options.browserstackParams["browserstack.accessibilityOptions"]
              ? options.browserstackParams["browserstack.accessibilityOptions"].authToken
              : options.browserstackParams["browserstack.accessibilityOptions.authToken"];
            const jwt = require('jsonwebtoken');
            const decodedAccessibilityToken = jwt.decode(accessibilityAuthToken);
            const runDomforge = decodedAccessibilityToken && decodedAccessibilityToken.a11y_core_config.hasOwnProperty('domForge') ? decodedAccessibilityToken.a11y_core_config.domForge : false;
            ['chromeOptions'].forEach(function(option){
              var options = tmp_json_parse['desiredCapabilities'] && tmp_json_parse['desiredCapabilities'][option];
              options['args'] = options['args'] || [];
              options['args'] = typeof(options['args']) == "string" ? [options['args']] : options['args'];
              options['args'].push(`--load-extension=${parsed_data['accessibilityExtensionPath']}`);
              if (runDomforge) {
                options['args'].push('--auto-open-devtools-for-tabs');
                options['prefs']['devtools']['preferences']['currentDockState'] = "\"undocked\"";
                options['prefs']['devtools']['preferences']['last-dock-state'] = "\"undocked\"";
              }
            });
          }
          if (isNotUndefined(parsed_data['chromium_bundle_id'])) {
            tmp_json_parse = tmp_json_parse || JSON.parse(options.post_data);
            const chromium_bundle_id = parsed_data['chromium_bundle_id'];
            const isW3c = ("capabilities" in tmp_json_parse);
            if (isW3c) {
              tmp_json_parse["capabilities"]["firstMatch"][0]["bundleId"] = chromium_bundle_id;
              delete tmp_json_parse["capabilities"]["firstMatch"][0]["browser"];
              delete tmp_json_parse["capabilities"]["firstMatch"][0]["browserName"];
            } else {
              tmp_json_parse["desiredCapabilities"]["bundleId"] = chromium_bundle_id;
              delete tmp_json_parse["desiredCapabilities"]["browser"];
              delete tmp_json_parse["desiredCapabilities"]["browserName"];
            }
          }

          if (tmp_json_parse) {
            options.post_data = JSON.stringify(tmp_json_parse);
          }

          helper.pushToCLS('prepare_terminal_got_response', {
            session_id: options.sessionId,
            user_id: options.user,
            get_params: HubLogger.filterDetails(get_data),
            terminal: options.hostname,
            response: parsed_data
          }, isAppAutomate);

          var firecmdResponseOptions = {'seleniumVersion' : parsed_data.seleniumVersion };
          if(( parsed_data["BrowserMob"] || "" ).match(/Got Error while starting BrowserMob. Moving ahead./i)) {
            firecmdResponseOptions.runSessionAfterBrowserMobFailed = true;
          }

          if (isNotUndefined(parsed_data['playwright_url'])) {
            firecmdResponseOptions.playwrightURL = parsed_data['playwright_url'];
          }

          if (isNotUndefined(parsed_data['detox_server_url'])) {
            firecmdResponseOptions.detoxURL = parsed_data['detox_server_url'];
          }

          helper.addToConsoleTimes(options.request, `fire-command-end`);
          callback(undefined, undefined, firecmdResponseOptions);
        } else {
          helper.addToConsoleTimes(options.request, `fire-command-end`);
          HubLogger.exceptionLogger("fireCommands", options.sessionId + " Error in response for fireCommands. Exception: " + data, options.host_name, get_url);

          if (options.bsCaps["app"] && options.realMobile) {
            if (constants.googleLoginUserErrors.includes(parsed_data.kind)) {
              HubLogger.exceptionLogger("Google Login", options.sessionId + " User Error" + data, options.host_name, get_url);
              handle_error_fire_command(options, parsed_data.kind, 1, callback, data); // doesn't retries if attempt = 1
            }
            else if (data.includes("Captcha encountered")) {
              HubLogger.exceptionLogger("Google Login", options.sessionId + " Captcha " + data, options.host_name, get_url);
              handle_error_fire_command(options, "captcha-on-login", 1, callback, data); // doesn't retries if attempt = 1
            } else if (data.includes("Incorrect username/password")) {
              HubLogger.exceptionLogger("Google Login", options.sessionId + " Incorrect google username/password " + data, options.host_name, get_url);
              handle_error_fire_command(options, "incorrect-google-creds", 1, callback, data); // doesn't retries if attempt = 1
            } else if (isNotUndefined(parsed_data.type) && parsed_data.type === constants.FIRECMD_USER_ERROR_STRING) {
              handle_error_fire_command(options, "start-error-firecmd-user", 1, callback, data);
            } else {
              handle_error_fire_command(options, "start-error-firecmd", attempt, callback, data);
            }
          }
          else if(data && data.includes("Exception in safari plugin")){
            // In case of firecmds due to safari plugin, we will instrument different to avoid it in our metrics and alerts.
            handle_error_fire_command(options, "firecmd-error-safari-plugin", attempt, callback, data);
          }
          else {
            handle_error_fire_command(options, "start-error-firecmd", attempt, callback, data);
          }
        }
      } catch(err) {
        helper.addToConsoleTimes(options.request, `fire-command-end`);
        HubLogger.exceptionLogger("fireCommands", options.sessionId + " Error while parsing and processing response in fireCommands. Exception: " + err.toString(), options.host_name, get_url);
        if (checkRproxy5xxError(res, fireCmdOptions, options, 'firecmd-rproxy-retry')) {
          options.ignore_terminal_on_retry = options.browserstackParams["browserstack.terminal_sub_region"]; // Retrying in diff region by default, as all errors are nw_issues. Will whitelist non-nw issue in the constant used below
        }
        handle_error_fire_command(options, "start-error-firecmd", attempt, callback, data);
      }
    })
    .catch((e) => {
      helper.addToConsoleTimes(options.request, `fire-command-end`);
      // In case of network issues retry in different sub-region - APS-11660
      const prod_name = options.post_params && options.post_params.isAppAutomate ? "app-automate" : "automate";
      const hhplatform = options.realMobile ? 'mobile' : 'desktop';
      options.ignore_terminal_on_retry = options.browserstackParams["browserstack.terminal_sub_region"]; // Retrying in diff region by default, as all errors are nw_issues. Will whitelist non-nw issue in the constant used below, APS-11761
      if (constants.NODE_RETRY_SAME_REGION_ERROR_CODES.includes(e.code)) {
        delete options.ignore_terminal_on_retry;
      }
      helper.PingZombie({
        'category': prod_name+'-firecmd-stats',
        'kind' : prod_name+'-firecmd-retry-diff-region',
        'timestamp': (Math.round(new Date().getTime() / 1000).toString()),
        'session_id': options.sessionId,
        'user_id': options.user,
        'os': options.bsCaps["platform"] || options.bsCaps["os"],
        "os_version": options.bsCaps["os_version"] || options.bsCaps["orig_os"],
        'browser': options.bsCaps["browserName"],
        'browser_version': options.bsCaps['browser_version'],
        'data': {'error_code': e.code, 'error_type': e.type, 'hub_region': constants.region, 'platform': hhplatform,  'framework': options.post_params.framework},
        'error': e.toString(),
        'region': options.browserstackParams["browserstack.terminal_region"],
        'device': options.bsCaps["device"]
      });
      if(prod_name == 'automate') {
        HubLogger.hoothoot.emit('automate_miscellaneous_errors', 1, { event_type: 'automate_firecmd_retry_diff_region', platform: hhplatform, product: 'automate', hub_region: constants.region, terminal_region: options.browserstackParams["browserstack.terminal_region"], error_code: e.code, error_type: e.type });
      }
      switch (e.type) {
        case 'ResponseError':
          HubLogger.exceptionLogger("fireCommands", options.sessionId + " Error while receiving response for fireCommands. Exception: " + e.toString(), options.host_name, get_url);
          break;
        case 'TimeoutError':
          HubLogger.miscLogger("fireCommands", options.sessionId + " Explicit Timeout while making request in fireCommands: for " + get_url, LL.WARN);
          options.ignore_terminal_on_retry = options.browserstackParams["browserstack.terminal_sub_region"]; // In case of timeout errors, we should ignore that region's terminal on retry.
          // break;
        case 'RequestError':
          HubLogger.exceptionLogger("fireCommands", options.sessionId + " Error while making request in fireCommands. Exception: " + e.toString(), options.host_name, get_url);
          break;
        default:
          HubLogger.exceptionLogger("fireCommands Exception", options.sessionId + " Exception: " + e.stack.toString(), options.host_name, get_url);
          throw e;
      }
      handle_error_fire_command(options, "start-error-firecmd", attempt, callback, e.toString());
    });
  } else {
    callback();
  }
}

var handle_final_attempt_error_fire_command = function(opts, error_message, firecmdParsedResponse) {
  if(error_message == 'start-error-firecmd') {
    if(firecmdParsedResponse && firecmdParsedResponse.error && firecmdParsedResponse.error.match(/while starting BrowserMob/i)) {
      error_message = 'start-error-firecmd-browsermob';
    }
  }

  const user = opts.post_params ? opts.post_params['u'] : constants.HOOTHOOT_DEFAULT_USER;

  helper.addToConsoleTimes(opts.request, `post-browserstack-end`);
  HubLogger.nodeErrorHandler(opts.request, opts.response, undefined, opts.host_name + ":5555" , opts.sessionId, error_message, undefined, undefined, _getLoggingOpts(opts.bsCaps, opts.browserstackParams), _getLoggingOpts(opts.bsCaps, opts.browserstackParams, opts.hoothootCanaryTags, opts.user_id, opts.sessionId), user, function(){
    if(opts.hasOwnProperty("sessionId")) {
      const logText = helper.getDashboardFireCmdMessage(opts.bsCaps);
      const stopLogText = (isNotUndefined(opts.bsCaps["app"])) ? logText: "BROWSER START-UP FAILURE";
      HubLogger.addStopToRawLogs(opts, opts.sessionId, stopLogText, 1, true, undefined, undefined);
    }
  });
};

var handle_error_fire_command = function(opts, error_message, attempt, callback, data) {
  const isAppAutomate = opts.post_params ? opts.post_params.isAppAutomate : false;
  helper.pushToCLS('prepare_terminal_error', {
    session_id: opts.sessionId,
    user_id: opts.user,
    error_message: error_message,
    terminal: opts.hostname,
    response: data
  }, isAppAutomate);

  var firecmdParsedResponse = helper.getParsedObjectOrEmpty(data);

  if (firecmdParsedResponse && firecmdParsedResponse.kind) {
    opts.bsCaps["error_type"] = firecmdParsedResponse.kind;
    opts.bsCaps["firecmd_error_message"] = firecmdParsedResponse.error || "";
    opts.bsCaps["error_from"] = firecmdParsedResponse.type || "";
    opts.bsCaps["error_meta_data"] = firecmdParsedResponse.meta_data || {};
  } else if (data) {
    opts.bsCaps["error_type"] = "unhandled_response";
    opts.bsCaps["firecmd_error_message"] = data;
    opts.bsCaps["error_from"] = "browserstack_error";
  } else {
    opts.bsCaps["error_type"] = "unknown_exception";
    opts.bsCaps["firecmd_error_message"] = "Firecmd Request Timed out or Unknown Firecmd exception";
    opts.bsCaps["error_from"] = "browserstack_error";
  }

  clearInterval(opts.relaxed_interval);
  if (attempt == 1) {
    handle_final_attempt_error_fire_command(opts, error_message, firecmdParsedResponse);
  }
  else {
    helper.pushToCLS('prepare_terminal_retry', {
      session_id: opts.sessionId,
      user_id: opts.user,
      error_message: error_message
    }, isAppAutomate);
    HubLogger.miscLogger("fireCommands", opts.sessionId + " Retrying fireCommands ", LL.WARN);
    callback(attempt+1, error_message, { opts, firecmdParsedResponse });
  }
};

var _getLoggingOpts = function(bsCaps, browserstackParams, hub_canary_tags, user_id, session_id) {
  if(isUndefined(bsCaps) || isUndefined(browserstackParams)) {
    return {};
  } else {
    var browser = bsCaps.app ? 'app' : (bsCaps['browser'] || bsCaps['browserName']);
    var retObj = {
      'os': bsCaps['os'] || bsCaps['platform'],
      'os_with_version': bsCaps['orig_os'],
      'browser': browser,
      'browser_version': bsCaps['version'] || bsCaps['device'],
      'device': browserstackParams["realMobile"] ? bsCaps["udid"] : (bsCaps['device'] ? bsCaps['device'] : false),
      'ieDriver' : browserstackParams['browserstack.ie.driver'],
      'terminal_type': browserstackParams["realMobile"] ? (bsCaps["app"] ? 'app_testing' : 'realMobile') : '',
      'appTesting': bsCaps["app"],
      'terminalSubRegion': browserstackParams["browserstack.terminal_sub_region"],
      'georestricted_region': browserstackParams['browserstack.georestricted_region'],
      'rails_session_id': session_id
    };

    if (bsCaps["app"] && browserstackParams["browserstack.appStoreConfiguration.username"]) {
      retObj['app_store_username'] = browserstackParams["browserstack.appStoreConfiguration.username"];
      retObj['app_store_password'] = browserstackParams["browserstack.appStoreConfiguration.password"];
    }
    //Set os_version as iPhone 7 Plus-10.0
    if(bsCaps["mobile"] && bsCaps["mobile"]["version"]) {
      retObj['os_version'] = bsCaps["mobile"]["version"];
    } else {
      retObj['os_version'] = bsCaps["os_version"] || bsCaps['device'] || bsCaps['orig_os'];
    }
    if (bsCaps["error_type"]) {
      retObj["error_type"] = bsCaps["error_type"];
      retObj["firecmd_error_message"] = bsCaps["firecmd_error_message"];
      retObj["error_meta_data"] = bsCaps["error_meta_data"];

      if (bsCaps["error_from"] == "user_error") {
        retObj["error_from"] = "start-error-firecmd-user";
      }
    }
    if (isNotUndefined(hub_canary_tags)) {
      retObj.hoothootCanaryTags = hub_canary_tags;
    }
    if (isNotUndefined(user_id)) {
      retObj.user_id = user_id;
    }
    return retObj;
  }
};


exports._getLoggingOpts = _getLoggingOpts;

var _getLoggingOptsRegistry = function(registryMapping){
  if(isUndefined(registryMapping)) {
    return {};
  } else {
    return {
      'os': registryMapping.os_passed_by_user,
      'os_version': registryMapping.os_version,
      'browser': registryMapping.browser,
      'browser_version': registryMapping.browser_version,
      'device': registryMapping["realMobile"] ? registryMapping["device"] : false,
      'terminal_type': registryMapping["realMobile"] ? 'realMobile' : '',
      'appTesting': registryMapping["app"]
    };
  }
};

var _appendOpts = function(original, new_opts){
  if(!new_opts) return original;
  Object.keys(new_opts).forEach(function(key){
    original[key] = new_opts[key];
  });
  return original;
};

exports.getLoggingOpts = _getLoggingOpts;
exports.getLoggingOptsRegistry = _getLoggingOptsRegistry;
exports.appendOpts = _appendOpts;

let check_edge_extension_sel3 = (caps, rails_omitted_caps) => {
  if (!rails_omitted_caps){
    return false;
  }
  const browserName = (caps['browserName'] || caps['browser'] || '').toLowerCase();
  const browserVersion = (caps['browser_version'] || caps['browserVersion'] || '');
  if (('chromeExtension' in rails_omitted_caps) && isEdgeChromium(browserName, browserVersion)) {
    return true;
  }
  return false;
};

exports.check_edge_extension_sel3 = check_edge_extension_sel3;

var add_rails_omitted_caps = function(caps, rails_omitted_caps){
  if (!rails_omitted_caps){
    return caps;
  }
  const browserName = (caps['browserName'] || caps['browser'] || '').toLowerCase();
  const browserVersion = (caps['browser_version'] || caps['browserVersion'] || '');
  for (var key in rails_omitted_caps){
    if (key === "mozfirefoxProfile" && browserName == "firefox") {
      caps['moz:firefoxOptions'] = caps['moz:firefoxOptions'] || {};
      caps['moz:firefoxOptions']['profile'] = rails_omitted_caps['mozfirefoxProfile'];
    } else if (key === 'chromeExtension' && (browserName == "chrome" || isEdgeChromium(browserName, browserVersion))) {
      caps['chromeOptions'] = caps['chromeOptions'] || {};
      if (Array.isArray(rails_omitted_caps['chromeExtension'])) {
        rails_omitted_caps['chromeExtension'] = rails_omitted_caps['chromeExtension'].filter(
          ext => !(typeof ext === "boolean" || (typeof ext === "string" && ext.trim() === ""))
        );
      }
      caps['chromeOptions']['extensions'] = rails_omitted_caps['chromeExtension'];
    } else {
      caps[key] = (caps[key] && caps[key]!="[REDACTED]") ? caps[key] : rails_omitted_caps[key];
    }
  }
  return caps;
};

var modify_safari_caps = function(lcaps, browserstackParams){
  delete browserstackParams["browserstack.chrome.driver"];
  if(lcaps["safari.options"] && (typeof lcaps["safari.options"] === 'object')){
    lcaps["safari.options"]["cleanSession"] = true;
    lcaps["safari.options"]["port"] = constants.safari_driver_port;
  } else {
    lcaps["safari.options"] = {
      "cleanSession": true,
      "port": constants.safari_driver_port
    };
  }
  if(lcaps['platform'] == 'MAC') {
    lcaps['os'] = 'OS X';
  }
  delete lcaps["moz:firefoxOptions"];
  delete lcaps["firefoxOptions"];
  const firstMatchArray = helper.nestedKeyValue(lcaps, ['W3C_capabilities', 'firstMatch']);
  if (Array.isArray(firstMatchArray) && helper.isHash(firstMatchArray[0])) {
    delete firstMatchArray[0]["moz:firefoxOptions"];
    delete firstMatchArray[0]["firefoxOptions"];
  }
};

var modify_ie_caps = function(lcaps){
  var version = parseFloat(lcaps.version);
  if (!lcaps["browserstack.edgeVersion"]) lcaps.initialBrowserUrl = 'about:blank';
  if(isUndefined(lcaps.nativeEvents) && version != 11) {
    lcaps.nativeEvents = false;
  }
  if (lcaps.acceptInsecureCerts) {
    lcaps.acceptSslCerts = helper.isTrue(lcaps.acceptInsecureCerts);
    delete lcaps.acceptInsecureCerts;
  }
  const firstMatchArray = helper.nestedKeyValue(lcaps, ['W3C_capabilities', 'firstMatch']);
  if (Array.isArray(firstMatchArray) && helper.isHash(firstMatchArray[0])) {
    firstMatchArray[0].browserName = 'internet explorer';
    const ieOptions = firstMatchArray[0]['se:ieOptions'] || {};
    if (!lcaps["browserstack.edgeVersion"]) ieOptions.initialBrowserUrl = 'about:blank';
    firstMatchArray[0]['se:ieOptions'] = ieOptions;
    delete firstMatchArray[0].acceptInsecureCerts;
  }
  return lcaps;
};

const modifyEdgeCaps = (lcaps) => {
  const browserName = (isEdgeChromium(lcaps.browser, lcaps.version) || isEdgeChromium(lcaps.browserName, lcaps.version)) ? 'edge' : 'MicrosoftEdge';
  lcaps.browser = browserName;
  lcaps.browserName = browserName;
  lcaps.initialBrowserUrl = 'about:blank';
  if (lcaps.acceptInsecureCerts) {
    lcaps.acceptSslCerts = helper.isTrue(lcaps.acceptInsecureCerts);
    delete lcaps.acceptInsecureCerts;
  }
  // Declare Edge 18.0 strict w3c compliant
  // Edge 15.0, 16.0, 17.0 supports both OSS and W3C but default is OSS
  const firstMatchArray = helper.nestedKeyValue(lcaps, ['W3C_capabilities', 'firstMatch']);
  if (Array.isArray(firstMatchArray) && helper.isHash(firstMatchArray[0])) {
    firstMatchArray[0].browserName = 'MicrosoftEdge';
    // Deleting browserVersion, acceptInsecureCerts for `Edge 18.0` {alias 43.0} since it gives `browserVersion, acceptInsecureCerts invalid error`
    if (firstMatchArray[0].browserVersion >= '43.0' && firstMatchArray[0].browserVersion <= '44.0') {
      delete firstMatchArray[0].browserVersion;
      delete firstMatchArray[0].acceptInsecureCerts;
    }
  }

  return lcaps;
};

var modify_firefox_caps = function(lcaps, browserstackParams){
  var version = parseFloat(lcaps["version"]);
  const seleniumVersion = browserstackParams["browserstack.selenium.jar.version"] || '0.0.0';
  const firstMatchArray = helper.nestedKeyValue(lcaps, ['W3C_capabilities', 'firstMatch']) || [{}];
  if(version && ( version == 53.0 || version == 54.0 ) && lcaps["unexpectedAlertBehaviour"]) {
    HubLogger.miscLogger("modifyFirefoxCaps", "Removing capability unexpectedAlertBehaviour for session: " + ( browserstackParams['browserstack.video.filename'] || "" ).replace(/video-/g, ''), LL.INFO);
    delete lcaps["unexpectedAlertBehaviour"];
  }

  if(version > 51) {
    delete lcaps.proxy;
    delete firstMatchArray[0].proxy;
  }
  if (helper.versionCompare(seleniumVersion, "3.7.0") < 0) {
    delete lcaps["W3C_capabilities"];
  }
};

const addHeadlessOptions = (lcaps, browserstackParams, browserName) => {
  if (browserstackParams['browserstack.headless'] == 'true' && ['CHROME', 'FIREFOX'].includes(browserName)) {
    let browser_options = (browserName == 'CHROME') ? 'chromeOptions' : 'moz:firefoxOptions';
    lcaps[browser_options] = lcaps[browser_options] || {};
    lcaps[browser_options]['args'] = lcaps[browser_options]['args'] || [];
    if (lcaps[browser_options]['args'].indexOf('--headless') === -1)
      lcaps[browser_options]['args'].push('--headless');
  }
};

var forceAddBfCache = function(browserstackParams, os, browser_version) {
  if(browserstackParams["browserstack.bfcache"] != "1" &&
      os.match(/win/) &&
      browser_version && browser_version.toString().match(/11/) !== null) {
    browserstackParams["browserstack.bfcache"] = "0";
  }
  return browserstackParams;
};

var fixLocalParams = function(browserstackParams) {
  if (browserstackParams.local_params) {
    try {
      browserstackParams["local_params"] = JSON.parse(browserstackParams["local_params"]);
    } catch (e) {
      HubLogger.exceptionLogger("localParams",  "Error in parse JSON local_params" + browserstackParams["local_params"] + " Exception: " + e, "", "");
    }
  }
};

var setBrowserProxy = (lcaps, browserstackParams, browserProxyOptions) => {
  var isNetworkLogsEnabled = browserProxyOptions.isNetworkLogsEnabled || false;
  let isRealMobile = lcaps.realMobile;

  var proxyObj = undefined;
  var browsers = ["opera", "chrome", "firefox", "safari"];

  var browser_version = lcaps["version"] || lcaps["browser_version"] || "";
  var intBrowserVersion = parseInt(browser_version);

  if(isNetworkLogsEnabled || ( isNotUndefined(browserstackParams["browserstack.ie.alternateProxy"]) && browserstackParams["browserstack.ie.alternateProxy"].toString() != "false")) {
    browsers.push("internet explorer");
  }

  if (lcaps['browserName'] && ['microsoftedge', 'edge'].indexOf(lcaps['browserName'].toLowerCase()) > -1 && !isNaN(intBrowserVersion) && intBrowserVersion >= 79) {
    browsers.push(lcaps['browserName'].toLowerCase());
  }

  var os = lcaps['os'] || lcaps['platform'];
  var windowsChrome = os && os.toLowerCase() === 'windows' && lcaps['browserName'] && lcaps['browserName'].toLowerCase() === 'chrome';
  var windowsEdgeChromium = os && os.toLowerCase() === 'windows' && lcaps['browserName'] && ['microsoftedge', 'edge'].indexOf(lcaps['browserName'].toLowerCase()) > -1 && !isNaN(intBrowserVersion) && intBrowserVersion >= 79;
  var isWindows10ie = lcaps["orig_os"] && lcaps["orig_os"].toLowerCase() === "win10" && lcaps['browser'] && (lcaps['browser'].toLowerCase() === 'ie' || lcaps['browser'].toLowerCase() === 'internet explorer');
  var macChrome = lcaps["orig_os"] && lcaps["orig_os"].match(/mac/) && lcaps['browserName'] && lcaps['browserName'].toLowerCase() === 'chrome';
  var macEdgeChromium = os && os.toLowerCase() === 'os x' && lcaps['browserName'] && ['microsoftedge', 'edge'].indexOf(lcaps['browserName'].toLowerCase()) > -1;

  if (lcaps["browserName"] && browsers.indexOf(lcaps["browserName"].toLowerCase()) > -1 && !isRealMobile) {
    if((browserstackParams["browserstack.tunnel"].toString() == "true" && !isWindows10ie) || (isNetworkLogsEnabled && (windowsChrome || windowsEdgeChromium))) {
      proxyObj = {};
      if (lcaps["setSocksProxy"] && (windowsEdgeChromium || macEdgeChromium || ["chrome", "firefox"].indexOf(lcaps["browserName"].toLowerCase()) > -1)) {
        if (lcaps["browserName"].toLowerCase() === "chrome") {
          proxyObj = undefined;
          const proxyServerArg = `--proxy-server=socks5://${browserstackParams["local_params"]["tunnelHostServer"]}:${browserstackParams["local_params"]["tunnelPorts"]}`;
          addChromeArgsToCaps(lcaps, proxyServerArg);
          if (intBrowserVersion > 70) {
            addChromeArgsToCaps(lcaps, '--proxy-bypass-list=<-loopback>');
          }
        } else if(parseFloat(lcaps["version"]) > 4) {
          proxyObj.proxyType = "MANUAL";
          proxyObj.socksProxy = browserstackParams["local_params"]["tunnelHostServer"] + ":" + browserstackParams["local_params"]["tunnelPorts"];
        }
      } else {
        proxyObj.proxyType = "PAC";
        if ((windowsEdgeChromium || windowsChrome || macChrome || macEdgeChromium) && intBrowserVersion && !isNaN(intBrowserVersion) && intBrowserVersion > 70) {
          const terminalServerPort = (windowsChrome || windowsEdgeChromium) ? 4567 : 45671;
          lcaps['proxy'] = proxyObj = undefined;
          addChromeArgsToCaps(lcaps, "--proxy-server=http://platform.browserstack.com:45696");
          // Support for file path as pac file url is removed from chrome
          // https://bugs.chromium.org/p/chromium/issues/detail?id=839566&q=pac&colspec=ID%20Pri%20M%20Stars%20ReleaseBlock%20Component%20Status%20Owner%20Summary%20OS%20Modified
          if (browserstackParams['browserstack.wsLocalSupport'] && browserstackParams['browserstack.wsLocalSupport'].toString().toLowerCase() === 'true') {
            addChromeArgsToCaps(lcaps, `--proxy-pac-url=http://platform.browserstack.com:${terminalServerPort}/pacfile`);
          }
          let proxy_list = "<-loopback>;";
          if (browserstackParams["browserstack.ai_enabled_session"] && browserstackParams["browserstack.ai_enabled_session"].toString() == "true" && !isRealMobile){
            // Adding TCG endpoints in bypass proxy list for chrome based browsers
            proxy_list += `*${AICommandHelper.getTcgEndpoint() || AICommandHelper.DEFAULT_TCG_ENDPOINT}*;`;
            proxy_list += `*${AICommandHelper.getTcgS3UploadEndpoint() || AICommandHelper.DEFAULT_TCG_S3_ENDPOINT}*;`;
            proxy_list += `*${AICommandHelper.DEFAULT_EDS_ENDPOINT}*;`;
          }
          addChromeArgsToCaps(lcaps, `--proxy-bypass-list=${proxy_list}`);
        } else if (lcaps["orig_os"].match(/mac/)) {
          proxyObj.proxyAutoconfigUrl = "http://localhost:45671/pacfile";
        } else if(lcaps["browserName"].toLowerCase() == "internet explorer") {
          proxyObj.proxyAutoconfigUrl = "http://localhost:4567/pacfile";
        } else {
          proxyObj.proxyAutoconfigUrl = "file://c:/Users/<USER>/proxy.pac";
        }
      }
    }
  } else if (lcaps["browserName"] && ["android", "chrome_android"].indexOf(lcaps["browserName"].toLowerCase()) > -1 && isRealMobile) {
    addChromeArgsToCaps(lcaps, "--proxy-bypass-list=<-loopback>");
  }

  // Add proxy-bypass for gc url in case of geoComply App
  if(browserstackParams && browserstackParams["browserstack.geoComplyApp"] && browserstackParams["browserstack.geoComplyApp"].toString().toLowerCase() === "true" && (macChrome || macEdgeChromium)) {
    addOrUpdateUrlToProxyBypassArg(lcaps, constants.GEOCOMPLY_PROXYBYPASS_URLS);
  }

  if(proxyObj && !( lcaps["browserName"] && lcaps["browserName"].toLowerCase() == 'firefox' && intBrowserVersion && !isNaN(intBrowserVersion) && intBrowserVersion >= 51 )) {
    lcaps["proxy"] = proxyObj;
  }
};

var addChromeArgsToCaps = function(caps, value) {
  caps['chromeOptions'] = caps['chromeOptions'] || {};
  caps['chromeOptions']['args'] = caps['chromeOptions']['args'] || [];
  caps["chromeOptions"]["args"].push(value);
};

var addOrUpdateUrlToProxyBypassArg = function(caps, value) {
  if (caps['chromeOptions'] && caps['chromeOptions']['args']) {
    // Find all indices of the proxy-bypass-list arguments
    const proxyArgIndices = caps['chromeOptions']['args']
      .map((arg, index) => arg.startsWith('--proxy-bypass-list=') ? index : -1)
      .filter(index => index !== -1);

    if (proxyArgIndices.length > 0) {
      // Get the last index of the proxy-bypass-list argument
      const lastProxyArgIndex = proxyArgIndices[proxyArgIndices.length - 1];

      // Extract the existing list
      let existingList = caps['chromeOptions']['args'][lastProxyArgIndex].replace('--proxy-bypass-list=', '');

      // Append the new value if it doesn't already exist
      if (!existingList.split(';').includes(value)) {
        caps['chromeOptions']['args'][lastProxyArgIndex] = `--proxy-bypass-list=${existingList};${value}`;
      }
    } else {
      // If no proxy-bypass-list argument exists, add a new one
      addChromeArgsToCaps(caps, `--proxy-bypass-list=${value}`);
    }
  } else {
    // If chromeOptions or args don't exist, add a new one
    addChromeArgsToCaps(caps, `--proxy-bypass-list=${value}`);
  }
};

const addEdgeDriverPathForIEOnEdgeMode = (lcaps, browserstackParams) => {
  if (lcaps["browserstack.edgeVersion"]) {
    const edgeVersion = parseInt(browserstackParams["browserstack.edge.driver"]);
    const browser = 'edge';
    for (let i=0; i < constants.browserConfig[browser].length; i++) {
      const browserConfig = constants.browserConfig[browser][i];
      if (parseInt(browserConfig.version) == edgeVersion) {
        const browserPath = getBrowserPath(lcaps, browserConfig);
        lcaps["se:ieOptions"] = lcaps["se:ieOptions"] || {};
        lcaps["se:ieOptions"]["ie.edgepath"] = browserPath;
      }
    }
  }
};

var fixCapablitiesForBrowserPath = function(lcaps, browserstackParams, fixCapabilitiesOptions) {
  if (!lcaps.realMobile) {
    fixCapabilitiesForOneBrowser(lcaps, browserstackParams,"firefox", "_binary");
    fixCapabilitiesForOneBrowser(lcaps, browserstackParams,"opera", ".binary");
    fixCapabilitiesForOneBrowser(lcaps, browserstackParams, "chrome", ".binary");
    fixCapabilitiesForOneBrowser(lcaps, browserstackParams, "edge", ".binary");
  }

  addEdgeDriverPathForIEOnEdgeMode(lcaps, browserstackParams);
  fixChrome(lcaps, browserstackParams);
  fixEdge(lcaps, browserstackParams);

  browserstackParams["browserstack.ie.noFlash"] = browserstackParams["browserstack.ie.noFlash"] || "false";
  lcaps["acceptSslCerts"] = lcaps["acceptSslCert"] = (lcaps["acceptSslCert"] || lcaps["acceptSslCerts"] || false).toString() == "true";

  setBrowserProxy(lcaps, browserstackParams, { isNetworkLogsEnabled: fixCapabilitiesOptions.isNetworkLogsEnabled });

  var browser_version = lcaps["version"] || lcaps["browser_version"] || "";
  var intBrowserVersion = parseInt(browser_version);

  if (!lcaps['app'] && (lcaps['browserName'] || lcaps['browser']).toLowerCase() === 'firefox') {
    /*
      Modify profile to not open learnmore page FF42
      profile["browser.startup.homepage"] = "about:blank"
      profile["startup.homepage_welcome_url"] = "about:blank"
      profile["startup.homepage_welcome_url.additional"] = "about:blank"
      profile["browser.usedOnWindows10.introURL"] = "about:blank?"
      profile['browser.cache.disk.enable'] = false;
      profile['browser.cache.memory.enable'] = false;
      profile['browser.cache.offline.enable'] = false;
      profile['network.http.use-cache'] = false;
      profile['network.proxy.socks_remote_dns'] = true;
    */
    const sessionId = (browserstackParams['browserstack.video.filename'] || '').replace(/video-/g, '');
    if (isUndefined(lcaps['firefox_profile'])) {
      HubLogger.miscLogger('fixCapabilitiesForOneBrowser', browserstackParams['browserstack.aws.save'] + ' Using Custom Firefox Profile on ' + lcaps['version'], LL.INFO);
      if (browserstackParams['browserstack.local'] && lcaps['version'].match(/beta/i))
        lcaps['firefox_profile'] = constants.FIREFOX_BETA_PROFILE;
      else if (browserstackParams['browserstack.local'] && lcaps['version'].replace('beta', '').trim() >= 67)
        lcaps['firefox_profile'] = constants.FIREFOX67_PROFILE;
      else if (browserstackParams['browserstack.local'] && lcaps['version'].replace('beta', '').trim() >= 65)
        lcaps['firefox_profile'] = constants.FIREFOX65_PROFILE;
      // adding updated profile with addition of native events to default profile only for geckodriver > 0.33
      else if (browserstackParams && browserstackParams['browserstack.geckodriver'] && helper.versionCompare(browserstackParams['browserstack.geckodriver'], '0.34.0') >= 0)
        lcaps['firefox_profile'] = constants.FIREFOX_PROFILE_WITH_NATIVE_EVENTS;
      else
        lcaps['firefox_profile'] = constants.FIREFOX_PROFILE;
    } else {
      helper.sendToEDS({
        kind: Events.AUTOMATE_TEST_SESSIONS,
        hashed_id: sessionId,
        secondary_diagnostic_reasons: 'custom-firefox-profile',
      });
      HubLogger.miscLogger('fixCapabilitiesForOneBrowser', `Customer Sent a custom firefox Profile. sessionId is: ${sessionId}`, LL.INFO);
    }

    // From Selenium 3.5.0+, moz:firefoxOptions is required instead of firefox_profile, firefox_binary etc.
    if (browserstackParams['browserstack.selenium.jar.version'] && helper.versionCompare(browserstackParams['browserstack.selenium.jar.version'], '3.5.2') >= 0 && lcaps['firefox_binary']) {
      if (!lcaps['moz:firefoxOptions']) {
        lcaps['moz:firefoxOptions'] = {};
      }
      if (lcaps['moz:firefoxOptions'].profile) {
        helper.sendToEDS({
          kind: Events.AUTOMATE_TEST_SESSIONS,
          hashed_id: sessionId,
          secondary_diagnostic_reasons: 'custom-firefox-profile',
        });
        HubLogger.miscLogger('fixCapablitiesForBrowserPath', `Customer Sent a custom firefox Profile. sessionId is: ${sessionId}`, LL.INFO);
      }

      lcaps['moz:firefoxOptions'].binary = lcaps['firefox_binary'];
      lcaps['moz:firefoxOptions'].profile = lcaps['moz:firefoxOptions'].profile || lcaps['firefox_profile'];
    }

    if (intBrowserVersion > 47) {
      delete lcaps['marionette'];
    }
  }
};

// Called in-between firecmd request and start-request to jar
var patchStartSession = function(firecmdOptions, startSessionOptions, triggerSessionStart) {
  var capabilitiesForJar = startSessionOptions.post_data;
  var browserstackParams = startSessionOptions.browserstackParams;
  if(firecmdOptions.runSessionAfterBrowserMobFailed) {
    setBrowserProxy(capabilitiesForJar, browserstackParams, { isNetworkLogsEnabled: false });
    startSessionOptions.recoveredBrowserMobError = true;
  }
  triggerSessionStart();
};

const generateRequestDelay = (retryMethod, attempt) => {
  let retryDelay = [0];
  switch (retryMethod) {
    case constants.LATE_RETRY: {
      retryDelay = constants.LATE_RETRY_DELAY;
      break;
    }
    default: {
      retryDelay = constants.DEFAULT_RETRY_DELAY;
      break;
    }
  }
  return retryDelay[attempt] || constants.DEFAULT_BACKUP_DELAY;
};

const requestToBrowserStackError = (url, postParams, request, response, callback, error, retry) => {
  HubLogger.bsErrorHandler(
    request,
    response,
    error,
    `BS threw error ${retry.attempt} time${retry.attempt > 1 ? 's' : ''} for single request`,
    (url + " : " + JSON.stringify(request ? request.headers : {}) + " : " + JSON.stringify(postParams)),
    postParams && postParams['u'],
    { isAppAutomate: postParams.isAppAutomate,
      isStartRequest: postParams.start == 'true',
      isDetox: (postParams["framework"] === constants.DETOX) }
  );
  const isStopRequest = url.match(/stop=/) || url.match(/release=/);
  const sessionId = isStopRequest ? querystring.parse(url).k : undefined;

  if (isStopRequest) {
    if (sessionId) {
      helper.handleSessionsWithStopFailedOnMaxRetries(sessionId, url.match(/app=true/));
    }
    if(callback) callback("{}");
  }
  return;
};

const getProductPackageNameBrief = (product_package) => {
  return product_package === constants.FUNCTIONAL_TESTING_PACKAGE_NAME ? constants.FT_PRODUCT_NAME_BRIEF : constants.CBT_PRODUCT_NAME_BRIEF;
};

const requestToBrowserStack = (url, postParams, request, response, callback, railsOmittedCaps, queueId, firecmdAttempt, startAttempt, e, indexCounter, postOptions, headers, retry) => {
  if (retry.shouldDrop) {
    return;
  }
  let prevSessionId;
  if (postParams["automation_session_id"]) {
    prevSessionId = postParams["automation_session_id"];
  }
  const originalDate = helper.getDate();
  const railsRequestStartTime = new Date();

  const isStopRequest = url.match(/stop=/) || url.match(/release=/);
  const isMediaRequest = url.match(/resolve_media/);
  const isMarkAsPercyRequest = url.match(/mark_as_percy=true/);
  const isAppAutomateSession = postParams.isAppAutomate;
  const isDetoxSession = (postParams["framework"] === constants.DETOX);
  const hostname = isAppAutomateSession ? constants.BS_APP_ENDPOINT : constants.BS_ENDPOINT;
  const pipelineIdentifier = postParams.pipelineIdentifier;
  let isMobile = helper.isMobile(postParams);
  let avoidAppium = false;
  if (retry.attempt === 0) {
    retry.firstRequestToBrowserStackTime = Date.now();
  }

  HubLogger.miscLogger(`reqToBrowserStack_${pipelineIdentifier}`, `firing call to browserstack for session ${prevSessionId || 'not-available'} with path ${postOptions.path}, appAutomate: ${isAppAutomateSession}`, LL.INFO);
  requestlib.call(postOptions)
  .then((proxyResponse) => {
    helper.addToConsoleTimes(request, 'check-rails-response-code');
    HubLogger.miscLogger(`reqToBrowserStack_${pipelineIdentifier}`, `session id ${prevSessionId || 'not-available'}, unregister pipeline request success, appAutomate: ${isAppAutomateSession}`, LL.INFO);
    return railsPipeline.railsResponsePipeline({
      isStopRequest: isStopRequest,
      identifier: pipelineIdentifier,
      username: postParams['u'],
      password: postParams['password'],
      response: proxyResponse,
      isAppAutomateSession: isAppAutomateSession,
      capabilities: postParams.desiredCapabilities
    });
  }).catch((railsRequestError) => {
    retry.attempt += 1;
    retry.requestDelay = generateRequestDelay(constants.DEFAULT_RETRY, retry.attempt);
    helper.addToConsoleTimes(request, 'check-rails-response-code');
    HubLogger.miscLogger(`reqToBrowserStack_${pipelineIdentifier}`, `session id ${prevSessionId || 'not-available'} unregister pipeline error: ${railsRequestError.toString()}, appAutomate: ${isAppAutomateSession}`, LL.INFO);
    return railsPipeline.railsResponsePipeline({
      isStopRequest: isStopRequest,
      identifier: pipelineIdentifier,
      username: postParams['u'],
      password: postParams['password'],
      requestError: railsRequestError,
      isAppAutomateSession: isAppAutomateSession,
      capabilities: postParams.desiredCapabilities
    });
  }).then(async (proxy_response) => {
    helper.addToConsoleTimes(request, 'check-rails-response-code-end');
    var bsdata = proxy_response.data;
    HubLogger.bsLogger(hostname, postOptions.path, headers, undefined, originalDate, pipelineIdentifier, LL.INFO);
    HubLogger.bsLogger(hostname, postOptions.path, headers, HubLogger.redactHashForKeys(bsdata), undefined, pipelineIdentifier, LL.INFO);
    if (proxy_response.statusCode != 200) {
      helper.addToConsoleTimes(request, `selauth-request-end`);
      // Post to Zombie
      HubLogger.seleniumStats(`hub_to_bs_retry`, {}, `Non 200 Response from BS ${proxy_response.statusCode}; retry.attempt is ${retry.attempt}`, "postParams: " + JSON.stringify(postParams) + " url: " + url + " response code:" + proxy_response.statusCode, "", "non-200-response", {isAppAutomate: isAppAutomateSession, isDetox: isDetoxSession});

      HubLogger.exceptionLogger("Non 200 response from BrowserStack. \nData: " + HubLogger.redactHashForKeys(bsdata) + "\nResponse: " + proxy_response.statusCode + "\nAttempt: " + retry.attempt,
        constants.BS_ENDPOINT, "/selenium/authenticate?auth="+constants.railstoken+url);
      if (proxy_response.statusCode === 500 || proxy_response.statusCode === 406) {
        retry.shouldDrop = true;
        if(postParams && postParams['u']) {
          var hoothootPlatform = isAppAutomateSession ? 'all' : isMobile ? 'mobile' : 'desktop';
          HubLogger.hoothoot_user.uniqueUserEvent(postParams['u'], (isAppAutomateSession ? 'app-automate' : 'automate'), 'hub_to_bs', hoothootPlatform, helper.getHubCanaryTag());
          if (!isAppAutomateSession) HubLogger.hoothoot_use.emit('automate_errors_data', 1, { event_type: 'hub_to_bs', platform: hoothootPlatform, product: 'automate', user: postParams['u'], status_code: proxy_response.statusCode, hub_region: constants.region});
        }
        var error = JSON.stringify({value: {message: "No response from BrowserStack!!"} , sessionId: "", "status": 13});
        if (isStopRequest) {
          if (callback) callback("{}");
        } else if(isMediaRequest){
          if (callback) callback(JSON.stringify({resolve_media: "failed"}));
        } else if (isMarkAsPercyRequest) {
          if (callback) callback(JSON.stringify({success: "false", error: `Status: ${proxy_response.statusCode}`}));
        }
        else if (response) {
          if(!isAppAutomateSession) {
            HubLogger.instrumentationStats('Rails Down', {user: postParams['u']}, 'No Response when postBrowserStack', error);
          }
          helper.respondWithError(request, response, error);
        }
      } else {
        retry.attempt += 1;
        if (proxy_response.statusCode === 404 || proxy_response.statusCode === 503 || proxy_response.statusCode === 504) {
          retry.maxRetryCount = constants.HUB_TO_BROWSERSTACK_RETRY[constants.LATE_RETRY].maxRetryCount;
          retry.requestDelay = generateRequestDelay(constants.LATE_RETRY, retry.attempt);
          retry.retryMethod = constants.LATE_RETRY;
          retry.maxRequestLife = constants.HUB_TO_BROWSERSTACK_RETRY[constants.LATE_RETRY].maxRequestLife;
          // replace key
          if (proxy_response.statusCode === 404) {
            url = (url.match(/stop=/) || url.match(/release=/)) ? url.replace(/[?&]k=([^&]*)?/g,"").replace("stop", "release") : url;
            postParams["r"] = (postParams["r"])? postParams["r"] + ". Attempt " + retry.attempt : `Stop request failed with ${proxy_response.statusCode} . Attempt ` + retry.attempt;
          }
        } else {
          retry.requestDelay = generateRequestDelay(constants.DEFAULT_RETRY, retry.attempt);
        }

        if(url && url.match(/reg_session/)) {
          HubLogger.miscLogger("reg_session Exception", "reg_session with data - " + postOptions.body + " - " + headers['content-length'] + "-" + postOptions.body.length + "-" + Buffer.byteLength(postOptions.body, 'utf-8'), LL.INFO);
        }
        HubLogger.exceptionLogger("Error communicating to BrowserStack. Reponse: " + proxy_response.statusCode + ", Attempt: " + retry.attempt, constants.BS_ENDPOINT, "/selenium/authenticate?auth="+constants.railstoken+url);
        if (retry.attempt < retry.maxRetryCount && (Date.now() - retry.firstRequestToBrowserStackTime) < retry.maxRequestLife) {
          await Promise.delay(request ? retry.requestDelay : 5 * retry.requestDelay);
          exports.postBrowserStack(url, postParams, request, response, callback, railsOmittedCaps, undefined, undefined, undefined, undefined, new Error("BS Response: " + proxy_response.statusCode), indexCounter, retry);
        }
        else {
          requestToBrowserStackError(url, postParams, request, response, callback, new Error("BS Response: " + proxy_response.statusCode), retry);
        }
      }
      return;
    }

    if (!request) {
      if(isStopRequest) helper.popNextSessionFromQueue(bsdata);
      if(callback) callback(bsdata);
      helper.addToConsoleTimes(request, `selauth-request-end`);
      return;
    } else {
      let jsonResponse = null;
      try {
        jsonResponse = JSON.parse(bsdata);
      } catch(error) {
        HubLogger.exceptionLogger(`CHECK requestToBrowserStack Exception: ${error.stack.toString()}, data: ${bsdata}`, hostname, "/selenium/authenticate?auth="+constants.railstoken+url);
        throw error;
      }
      var created_at = jsonResponse["created_at"];
      // To measure Setting device time for AppAutomate to show on dashboard
      if (created_at) {
        request.session_created_at = created_at;
      }
      // Do not remove above block
      if (jsonResponse["error"]) {
        helper.addToConsoleTimes(request, `selauth-request-end`);
        let { automate_error_data: automateErrorData } = jsonResponse;
        var endSession = function () {
          if (automateErrorData && !automateErrorData.raw_capabilities['browserstack.machine']) {
            automateErrorData = isAppAutomateSession ? { ...automateErrorData, kind: Events.APP_AUTOMATE_ERROR_DATA, framework: 'appium'} : { ...automateErrorData, kind: Events.AUTOMATE_ERROR_DATA};
            helper.sendToEDS(automateErrorData);
          }

          if (automateErrorData && automateErrorData.error_code_str === "no-parallel-session") {
            HubLogger.miscLogger(`noParallel_${postParams['u']}`, `no-parallel-session not queueing anymore - ${request.id}`, LL.INFO);
          }

          if(jsonResponse["error"] === constants.railsResponses.bad_auth) {
            response.writeHead(401);
          }
          let responseData = { value: { error: jsonResponse['error'], message: jsonResponse['error'] }, sessionId: "", "status": 13 };

          if (jsonResponse["queue_size_exceeded"]) {
            responseData.value.error = constants.QUEUE_SIZE_EXCEEDED.errorMessage;
            responseData.value.message = constants.QUEUE_SIZE_EXCEEDED.errorMessage;
          }

          var data1 = JSON.stringify(responseData);
          if (!isAppAutomateSession) {
            HubLogger.instrumentationStats('Error with Response 200 _postBrowserStack', {user: postParams['u']}, '', data1);
          }
          helper.respondWithError(request, response, data1);

          filterAndSendToZombie(jsonResponse, postParams, {isAppAutomate: isAppAutomateSession, isDetox: isDetoxSession});
        };

        if(jsonResponse["queue"]){
          if (automateErrorData) {
            if(postParams['u']) {
              // Lets store username and user_id incase a request gets queued
              // after hitting rails
              helper.redisClient.setex(constants.queueingRelatedEdsConst.keyPrefix + postParams['u'], constants.queueingRelatedEdsConst.timeout, automateErrorData.user_id);
            }
            let queueingData = {
              kind: Events.AUTOMATE_QUEUEING_DATA,
              request_id: request.id,
              queued_at: new Date(),
              queue_reason: automateErrorData.error_code_str,
              queued_user_tests: jsonResponse.queue_utilized,
              user: { user_id: automateErrorData.user_id },
            };
            if(isAppAutomateSession) {
              queueingData = { ...queueingData, kind: Events.APP_AUTOMATE_QUEUEING_DATA, framework: 'appium'};
            }
            helper.sendToEDS(queueingData);
          }
          return callback(request, response, false, false, false, {
            queue_id: queueId || helper.randomID(10),
            queueOptionsInRailsResponse: jsonResponse,
            queue_reason: jsonResponse["reason"],
            attempt: retry.attempt,
            firecmd_attempt: firecmdAttempt,
            start_attempt: startAttempt
          }, true, endSession, jsonResponse['queue_utilized'], jsonResponse['queue_limit'], jsonResponse['parallel_utilized'], jsonResponse['parallel_limit']);
        } else {
          helper.addToConsoleTimes(request, 'time-in-queue-end');
        }

        endSession();
        return;
      }
      if(isNotUndefined(queueId)) {
        helper.addToConsoleTimes(request, 'time-in-queue-end');
      }
      if (jsonResponse["sessions_list"]){
        helper.addToConsoleTimes(request, `selauth-request-end`);
        var client_write_data = "[]";
        try{
          client_write_data = JSON.stringify({
            value: jsonResponse.sessions_list,
            sessionId: null,
            status: 0,
          });
        } catch(err) {
          HubLogger.miscLogger("SessionsList", "Error while giving sessions list for url: " + url, LL.WARN);
        } finally {
          response.end(client_write_data);
        }
        return;
      }

      if (isNotUndefined(jsonResponse['queue_utilized']) && postParams['u']) {
        queueHandler.updateQueue(postParams['u'], jsonResponse['queue_utilized'], jsonResponse['queue_limit'], jsonResponse['parallel_utilized'], jsonResponse['parallel_limit'], postParams.isAppAutomate, postParams.isFunctionalTesting);
      }

      var host_name = jsonResponse["ip"];
      var rproxyHost = jsonResponse["rproxy_host"] ? jsonResponse["rproxy_host"] : host_name;
      var automation_session_id = jsonResponse["automation_session_id"];
      var build_hash = jsonResponse["build_hash"];
      var collection_number = jsonResponse["collection_number"];
      var session_user_id = jsonResponse["user_id"];
      var session_group_id = jsonResponse["group_id"];
      var lcaps = jsonResponse["desiredCapabilities"];
      var hoothootCanaryTags = jsonResponse["hoothoot_canary_tags"];
      var group_plan_type = jsonResponse["group_plan_type"];
      var privoxy_domain_control_flag = jsonResponse["privoxy_domain_control_flag"];
      var desktop_telemetry_enabled = jsonResponse["desktop_telemetry_enabled"];
      var desktop_telemetry_interval = jsonResponse["desktop_telemetry_interval"];
      var group_risk_bucket = jsonResponse["group_risk_bucket"];
      var bundle_id_block_flag = jsonResponse["bundle_id_block_flag"];
      var ios_msg_logging_flag = jsonResponse["ios_msg_logging_flag"];

      var browserstackParams = jsonResponse["browserstack_params"];
      var machine_key = jsonResponse["machine_key"];


      // adding data to socket for hub timeouts
      try {
        request.socket.sessionId = browserstackParams['browserstack.aws.save'].split('/')[1];
        request.socket.appSession = isAppAutomateSession;
      } catch (e) {
        HubLogger.newCGLogger("requestToBrowserStack", "Error in setting userPath to socket", LL.DEBUG);
      }

      const platformDetails = {
        platformName: jsonResponse['os'],
        platformVersion: jsonResponse['os_version']
      };

      if ('app' in lcaps) {
        delete lcaps['browser'];
        delete lcaps['browserName'];
      } else {
        delete lcaps['appPackage'];
        delete lcaps['appActivity'];
      }

      var browserName = (lcaps["browserName"] || lcaps["browser"] || "ANY").toUpperCase();
      var browserVersion = lcaps["browser_version"] || lcaps["version"];
      if(browserName == "SAFARI"){
        modify_safari_caps(lcaps, browserstackParams);
      } else if (browserName == "INTERNET EXPLORER" || browserName == "IE") {
        lcaps = modify_ie_caps(lcaps);
        browserstackParams = forceAddBfCache(browserstackParams, lcaps["orig_os"], browserVersion);
      } else if (browserName == "EDGE"){
        lcaps = modifyEdgeCaps(lcaps);
      } else if(browserName == "FIREFOX") {
        modify_firefox_caps(lcaps, browserstackParams);
      }

      //add browserOptions --headless if browserstack.headless is true
      addHeadlessOptions(lcaps, browserstackParams, browserName);

      helper.addAccessibilityAutomationPresignedURL(browserstackParams, railsOmittedCaps);

      fixLocalParams(browserstackParams);
      var post_json = add_rails_omitted_caps(JSON.parse(JSON.stringify(lcaps)), railsOmittedCaps);
      let has_edge_extension = check_edge_extension_sel3(post_json, railsOmittedCaps);
      if (post_json['browserstack.hosts']) {
        browserstackParams['browserstack.hosts'] = post_json['browserstack.hosts'];
      }

      // add realMobile by default to post_json
      if (browserstackParams.hasOwnProperty('realMobile')) {
        post_json.realMobile = browserstackParams.realMobile;
      }
      /*
      NOTE : FOR AUTOMATE ONLY
      Flag to disable privoxy on android devices.
      This is being added since privoxy doesn't go well with the `arg` "--host-resolver-rules" of chromeOptions
      and overrides with what the proxy-server says so.
      The Boolean value will be used to :
      1. Prevent the passing of `proxy-server` arg in chromeOptions
      2. Prevent setting of device level proxy on mobile
      In case local is enabled, local will be given more priority and the value returned will be `false`.
      If mobile data feature is enabled on a private sim device, then also the proxy will be not set.
      i.e., the usual flow of setting the proxy will be followed
      */
      var isDedicatedDevice = browserstackParams['browserstack.dedicatedDevice'] && browserstackParams['browserstack.dedicatedDevice'].toString() == "true";
      var isMobileDataFlagPresent = helper.isDefined(browserstackParams["extraAccess"]) && browserstackParams["extraAccess"].toString().toLowerCase().includes("mobile_data");
      const doNotSetProxy = helper.hostResolverHonored(post_json["platform"], post_json["chromeOptions"], browserstackParams["browserstack.tunnel"], isDedicatedDevice, isMobileDataFlagPresent);

      if(isNotUndefined(browserstackParams["browserstack.privoxy"]) && browserstackParams["browserstack.privoxy"].toString() == "true"){
        delete post_json["setSocksProxy"];
        delete lcaps["setSocksProxy"];
      }

      var isNetworkLogsEnabled = browserstackParams["browserstack.networkLogs"] && browserstackParams["browserstack.networkLogs"].toString() == "true";
      fixCapablitiesForBrowserPath(post_json, browserstackParams,{ isNetworkLogsEnabled: isNetworkLogsEnabled });
      if(postParams["queue_times"]) {
        // Reset the number of times queue_id was queued as a terminal has been assigned
        postParams["queue_times"] = 0;
      }

      delete post_json["is_snapshot"];
      delete post_json["isSmartTV"];
      delete post_json["setSocksProxy"];
      delete post_json["backfill"];
      delete post_json["keychainBioAuth"];
      delete post_json["inject_app"];
      delete post_json["chooserIntentSupport"];
      delete post_json["zip_align"];
      delete post_json["localization"];
      delete post_json["disableAnimations"];
      delete post_json['app_automate_custom_params'];
      delete post_json['appium:app'];
      delete post_json['custom_headers'];
      delete post_json['enable_proxy_for_insecure_websockets'];
      delete post_json['insecure_ws_proxy_params'];
      delete post_json['enable_bypass_local_server_request_for_patrol'];

      // TODO : logging related caps and conditions should be moved to railsApp in the method "modify_console_logging_caps"
      // Since the code is unnecessarily separated in hub and railsApp. Hub should only forward the output caps received ideally
      // from railsApp and not modify it too much.
      // Also, w3c requires different format of cap for enabling console logs. That's currently set in railsApp so as to
      // initiate the shifting of the code. Check "def extract_and_set_W3C_caps" in railsApp
      if('browserstack.logging' in browserstackParams && browserstackParams['browserstack.logging'].toString() != "false"){
        post_json["loggingPrefs"] = { "client": "ALL", "driver": "ALL", "browser": "ALL" };
        if(constants.LOG_TYPES.indexOf(browserstackParams['browserstack.logging']) > -1){
          post_json["loggingPrefs"][browserstackParams['browserstack.logging']] = "ALL";
        }
        if('browserstack.console' in browserstackParams && browserName == "CHROME") {
          post_json["loggingPrefs"] = Object.assign(post_json["loggingPrefs"] || {}, { "browser": browserstackParams['browserstack.console'] });
        }
      } else if(browserName == "CHROME" || browserName == "ANDROID" || browserName == "CHROME_ANDROID") {
        if('browserstack.console' in browserstackParams && browserstackParams['browserstack.console'].toString() != 'error') {
          post_json["loggingPrefs"] = Object.assign(post_json["loggingPrefs"] || {} ,{ "browser": browserstackParams['browserstack.console'] });
        } else {
          post_json["loggingPrefs"] = Object.assign(post_json["loggingPrefs"] || {} , {"browser": 'SEVERE'});
        }
      } else if(browserName == "FIREFOX" && 'browserstack.console' in browserstackParams && !isCDP(post_json)) {
        // Exluding call to this method for puppeteer & playwright
        // This conflicts with remote debugger port provided for ws connection
        helper.setOptionsToStartRemoteDebuggerFirefox(post_json);
      }
      var iosVersion = undefined;
      if(browserName == "IPHONE" || browserName == "IPAD" || browserName === "APPLETV" || (("app" in post_json) && (post_json["platformName"] == "ios" || post_json["platformName"] === "tvos"))){
        // This flow handles the App Automate's and Automate's w3c changes as well along with OSS
        var orig_os = jsonResponse["desiredCapabilities"]["orig_os"];
        let w3cCapsPresent = ("W3C_capabilities" in post_json);
        if (orig_os != "ios" && orig_os != "tvos" && orig_os != "macyos" && orig_os != "macmav" && orig_os != "macml" && orig_os != "macelc") {
          avoidAppium = true;
        } else {
          var parts = jsonResponse["desiredCapabilities"]["mobile"]["version"].split("-");
          if(orig_os == "macml" && parts[0].toLowerCase() != "ipad 3rd (6.0)" && parts[0].toLowerCase() != "iphone 4s (6.0)"){
            avoidAppium = true;
          } else {
            if(w3cCapsPresent) {
              if( browserName === "APPLETV" || post_json["platformName"] === "tvos" ){
                post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = "tvos";
              } else {
                post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = "iOS";
              }
              if (!("app" in post_json)){
                post_json["W3C_capabilities"]["firstMatch"][0]["browserName"] = "safari";
              }
              post_json["W3C_capabilities"]["firstMatch"][0]["realMobile"] = browserstackParams["realMobile"] || false;
            } else {
              if (!("app" in post_json)) {
                post_json["browserName"] = "safari";
              }
              if( browserName === "APPLETV" || post_json["platformName"] === "tvos" ){
                post_json["platformName"] = "tvos";
              } else {
                post_json["platformName"] = "iOS";
              }

              post_json["realMobile"] = browserstackParams["realMobile"] || false;
            }
            iosVersion = parts[1];

            // No need to set platformVersion for iOS NJB
            // as the platformVersion capability is set in the default capabilities for Appium (present in the plist)
            if (parseInt(iosVersion) < 10) {
              if(w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["platformVersion"] = iosVersion;
              } else {
                post_json["platformVersion"] = iosVersion;
              }
            }

            if (parts[0].toLowerCase() == "ipad mini 2" || parts[0].toLowerCase() == "ipad mini 2 (9.1)" ||
                parts[0].toLowerCase() == "ipad mini 4" || parts[0].toLowerCase() == "ipad mini 4 (9.1)") {
              if(w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = "iPad Retina";
                post_json["W3C_capabilities"]["firstMatch"][0]["device"] = "iPad Retina";
              } else {
                post_json["deviceName"] = "iPad Retina";
                post_json["device"] = "iPad Retina";
              }
            } else {
              if (parts[0].indexOf("(") > -1){
                if(w3cCapsPresent) {
                  post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = parts[0].substring(0, parts[0].indexOf("(") - 1);
                } else {
                  post_json["deviceName"] = parts[0].substring(0, parts[0].indexOf("(") - 1);
                }
              } else {
                if(w3cCapsPresent) {
                  post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = parts[0];
                } else {
                  post_json["deviceName"] = parts[0];
                }
              }
            }
            if((jsonResponse["desiredCapabilities"]["acceptSslCerts"] || "").toString() == "true" && !isFalseString(jsonResponse["desiredCapabilities"]["autoAcceptAlerts"])) {
              if(w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["autoAcceptAlerts"] = true;
              } else {
                post_json["autoAcceptAlerts"] = true;
              }
            }

            if(w3cCapsPresent) {
              post_json["W3C_capabilities"]["firstMatch"][0]["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;
              post_json["W3C_capabilities"]["firstMatch"][0]["safariIgnoreFraudWarning"] = true;
              post_json["W3C_capabilities"]["firstMatch"][0]["orientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
              post_json["W3C_capabilities"]["firstMatch"][0]["deviceOrientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
            } else {
              post_json["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;
              post_json["safariIgnoreFraudWarning"] = true;
              post_json["orientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
              post_json["deviceOrientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
            }

            if (isNotUndefined(jsonResponse["desiredCapabilities"]["nativeWebTap"])) {
              if (w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["nativeWebTap"] = jsonResponse["desiredCapabilities"]["nativeWebTap"];
              } else {
                post_json["nativeWebTap"] = jsonResponse["desiredCapabilities"]["nativeWebTap"];
              }
            }

            if (isNotUndefined(jsonResponse["desiredCapabilities"]["autoAcceptAlerts"])) {
              if (w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["autoAcceptAlerts"] = jsonResponse["desiredCapabilities"]["autoAcceptAlerts"];
              } else {
                post_json["autoAcceptAlerts"] = jsonResponse["desiredCapabilities"]["autoAcceptAlerts"];
              }
            }

            if (iosVersion.trim() == "7.0") {
              if(w3cCapsPresent) {
                post_json["W3C_capabilities"]["firstMatch"][0]["webviewConnectRetries"] = 12;
                post_json["W3C_capabilities"]["firstMatch"][0]["waitForAppScript"] = "$.delay(5000); true;";
                post_json["W3C_capabilities"]["firstMatch"][0]["nativeInstrumentsLib"] = true;
              } else {
                post_json["webviewConnectRetries"] = 12;
                post_json["waitForAppScript"] = "$.delay(5000); true;";
                post_json["nativeInstrumentsLib"] = true;
              }
            }

            if(isNotUndefined(browserstackParams["browserstack.safari.allowAllCookies"])) {
              if(browserstackParams["browserstack.safari.allowAllCookies"].toString() == "true") {
                if (w3cCapsPresent) {
                  const { W3C_capabilities: { firstMatch: [capsEntry = {}] = [] } = {} } = post_json;
                  capsEntry["acceptCookies"] = "always";
                } else {
                  post_json["acceptCookies"] = "always";
                }
              }
            }

            if(w3cCapsPresent) {
              post_json["W3C_capabilities"]["firstMatch"][0]["noReset"] = true;
            } else {
              post_json["noReset"] = true;
            }
          }
        }
      } else if (browserName.toLowerCase().match(/chromium_(iphone|ipad)/) && !("app" in post_json)) {
        let w3cCapsPresent = ("W3C_capabilities" in post_json);
        let W3C_capabilities = post_json["W3C_capabilities"] ? post_json["W3C_capabilities"] : null;
        let parts = jsonResponse["desiredCapabilities"]["mobile"]["version"].split("-");
        if(w3cCapsPresent) {
          post_json["W3C_capabilities"] = W3C_capabilities;
          post_json["W3C_capabilities"]["firstMatch"][0]["realMobile"] = browserstackParams["realMobile"] || false;
          post_json["W3C_capabilities"]["firstMatch"][0]["noReset"] = true;
          post_json["W3C_capabilities"]["firstMatch"][0]["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;
          post_json["W3C_capabilities"]["firstMatch"][0]["orientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
          post_json["W3C_capabilities"]["firstMatch"][0]["deviceOrientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
          post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = "iOS";
        } else {
          post_json["realMobile"] = browserstackParams["realMobile"] || false;
          post_json["noReset"] = true;
          post_json["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;
          post_json["orientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
          post_json["deviceOrientation"] = (jsonResponse["desiredCapabilities"]["deviceOrientation"] || "portrait").toString().toUpperCase();
          post_json["platformName"] = "iOS";
        }
        if((jsonResponse["desiredCapabilities"]["acceptSslCerts"] || "").toString() == "true" && !isFalseString(jsonResponse["desiredCapabilities"]["autoAcceptAlerts"])) {
          if(w3cCapsPresent) {
            post_json["W3C_capabilities"]["firstMatch"][0]["autoAcceptAlerts"] = true;
          } else {
            post_json["autoAcceptAlerts"] = true;
          }
        }
        if (isNotUndefined(jsonResponse["desiredCapabilities"]["nativeWebTap"])) {
          if (w3cCapsPresent) {
            post_json["W3C_capabilities"]["firstMatch"][0]["nativeWebTap"] = jsonResponse["desiredCapabilities"]["nativeWebTap"];
          } else {
            post_json["nativeWebTap"] = jsonResponse["desiredCapabilities"]["nativeWebTap"];
          }
        }
        if (isNotUndefined(jsonResponse["desiredCapabilities"]["autoAcceptAlerts"])) {
          if (w3cCapsPresent) {
            post_json["W3C_capabilities"]["firstMatch"][0]["autoAcceptAlerts"] = jsonResponse["desiredCapabilities"]["autoAcceptAlerts"];
          } else {
            post_json["autoAcceptAlerts"] = jsonResponse["desiredCapabilities"]["autoAcceptAlerts"];
          }
        }
        if (parts[0].indexOf("(") > -1){
          if(w3cCapsPresent) {
            post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = parts[0].substring(0, parts[0].indexOf("(") - 1);
          } else {
            post_json["deviceName"] = parts[0].substring(0, parts[0].indexOf("(") - 1);
          }
        } else {
          if(w3cCapsPresent) {
            post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = parts[0];
          } else {
            post_json["deviceName"] = parts[0];
          }
        }
      } else if ((browserName.toLowerCase() === "samsung" && post_json.realMobile) && !("app" in post_json)) {
        let chromeOptions = post_json["chromeOptions"];
        let W3C_capabilities = post_json["W3C_capabilities"] ? post_json["W3C_capabilities"] : null;
        let deviceVersionString = jsonResponse["desiredCapabilities"]["mobile"]["version"].toString();
        let apiVersion = Number(deviceVersionString.split("-")[1]);
        if (W3C_capabilities) {
          post_json["W3C_capabilities"] = W3C_capabilities;
          post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = constants.SAMSUNG_MOBILE_CONSTANTS["platformName"];
          post_json["W3C_capabilities"]["firstMatch"][0]["appPackage"] = constants.SAMSUNG_MOBILE_CONSTANTS["appPackage"];
          post_json["W3C_capabilities"]["firstMatch"][0]["appActivity"] = constants.SAMSUNG_MOBILE_CONSTANTS["appActivity"];
          post_json["W3C_capabilities"]["firstMatch"][0]["noReset"] = true;
        } else {
          post_json["platformName"] = constants.SAMSUNG_MOBILE_CONSTANTS["platformName"];
          post_json["appPackage"] = constants.SAMSUNG_MOBILE_CONSTANTS["appPackage"];
          post_json["appActivity"] = constants.SAMSUNG_MOBILE_CONSTANTS["appActivity"];
          post_json["noReset"] = true;
          delete post_json["browserName"]; // The desired should not include both of an 'appPackage' and a 'browserName'
        }
        post_json["platformVersion"] = apiVersion.toString();
        post_json["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;

        post_json['chromeOptions'] = chromeOptions || {};

        if(helper.isHash(post_json["chromeOptions"])) {
          post_json["chromeOptions"]["androidDeviceSocket"] = constants.SAMSUNG_MOBILE_CONSTANTS["androidDeviceSocket"];
          post_json["chromeOptions"]["androidExecName"] = constants.SAMSUNG_MOBILE_CONSTANTS["androidExecName"];
        }

      } else if ((browserName.toLowerCase() === "android" || (browserName.toLowerCase() === "chrome_android" && post_json.realMobile)) && !("app" in post_json)) {
        // This flow handles only Automate's w3c and oss cases
        var deviceVersionString = jsonResponse["desiredCapabilities"]["mobile"]["version"].toString();
        var apiVersion = Number(deviceVersionString.split("-")[1]);
        if (apiVersion < 4.4) {
          avoidAppium = true;
          delete post_json["browserName"];
          post_json["browser"] = "android";
        } else {
          var chromeOptions = post_json["chromeOptions"];
          let W3C_capabilities = post_json["W3C_capabilities"] ? post_json["W3C_capabilities"] : null;
          if (W3C_capabilities) {
            post_json["W3C_capabilities"] = W3C_capabilities;
            post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = "Android";
          } else {
            post_json["platformName"] = "Android";
          }
          post_json["platformVersion"] = apiVersion.toString();
          post_json["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;

          post_json["realMobile"] = browserstackParams["realMobile"] || false;

          if (jsonResponse["desiredCapabilities"]["unicodeKeyboard"]) {
            if (W3C_capabilities) {
              post_json["W3C_capabilities"]["firstMatch"][0]["unicodeKeyboard"] = jsonResponse["desiredCapabilities"]["unicodeKeyboard"];
            } else {
              post_json["unicodeKeyboard"] = jsonResponse["desiredCapabilities"]["unicodeKeyboard"];
            }
          }

          if (jsonResponse["desiredCapabilities"]["resetKeyboard"]) {
            if (W3C_capabilities) {
              post_json["W3C_capabilities"]["firstMatch"][0]["resetKeyboard"] = jsonResponse["desiredCapabilities"]["resetKeyboard"];
            } else {
              post_json["resetKeyboard"] = jsonResponse["desiredCapabilities"]["resetKeyboard"];
            }
          }

          post_json["enablePerformanceLogging"] = jsonResponse["desiredCapabilities"]["enablePerformanceLogging"] || false;
          post_json['chromeOptions'] = chromeOptions || {};

          // Do not try to sanitize 'args' if chromeOptions is not a hash
          // typeof returns 'object' for Array / Hash. We sanitize 'args' only for Hash
          if(helper.isHash(post_json['chromeOptions'])) {
            post_json['chromeOptions']['args'] = helper.sanitizeChromeOptionsArgsArray(post_json['chromeOptions']['args']);
            post_json['chromeOptions']['args'].push('test-type');

            if(post_json["realMobile"].toString() != "false" && deviceVersionString.match(/Google Nexus 5/i)) {
              post_json['chromeOptions']['args'].push("--no-default-browser-check", "--no-first-run");
            }
          }
          if('browserstack.console' in browserstackParams && browserstackParams['browserstack.console'].toString() != 'error') {
            post_json["loggingPrefs"] =  Object.assign(post_json["loggingPrefs"] || {}, { "browser": browserstackParams['browserstack.console'] });
          } else {
            post_json["loggingPrefs"] =  Object.assign(post_json["loggingPrefs"] || {}, { "browser": 'SEVERE' });
          }


          if (post_json["realMobile"].toString() != "false") {
            // For Automate in Case of Real Mobiles
            if (post_json.hasOwnProperty("W3C_capabilities")) {
              post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = "android";
              post_json["W3C_capabilities"]["firstMatch"][0]["browserName"] = "chrome";
            } else {
              post_json["deviceName"] = "Android";
              post_json["browserName"] = "chrome";
            }
          } else {
            // For Emulators
            if (post_json.hasOwnProperty("W3C_capabilities")) {
              post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = "Android Emulator";
              post_json["W3C_capabilities"]["firstMatch"][0]["browserName"] = "Browser";
            } else {
              post_json["deviceName"] = "Android Emulator";
              post_json["browserName"] = "Browser";
            }
          }

          if((jsonResponse["desiredCapabilities"]["acceptSslCerts"] || "").toString() == "true")
            post_json["acceptSslCerts"] = true;
        }
      } else if (("app" in post_json) && (post_json["platformName"] == "android")) {
        // This flow is only for App Automate to handle the w3c changes
          let W3C_capabilities = ("W3C_capabilities" in post_json);
          if (W3C_capabilities) {
            post_json["W3C_capabilities"]["firstMatch"][0]["platformName"] = "Android";
            post_json["W3C_capabilities"]["firstMatch"][0]["deviceName"] = "Android";
          } else {
            post_json["platformName"] = "Android";
            post_json["deviceName"] = "Android";
          }
          post_json["newCommandTimeout"] = jsonResponse["desiredCapabilities"]["newCommandTimeout"] || 0;
          post_json["realMobile"] = browserstackParams["realMobile"] || false;

          if (jsonResponse["desiredCapabilities"]["unicodeKeyboard"]) {
            if (W3C_capabilities) {
              post_json["W3C_capabilities"]["firstMatch"][0]["unicodeKeyboard"] = jsonResponse["desiredCapabilities"]["unicodeKeyboard"];
            } else {
              post_json["unicodeKeyboard"] = jsonResponse["desiredCapabilities"]["unicodeKeyboard"];
            }
          }

          if (jsonResponse["desiredCapabilities"]["resetKeyboard"]) {
            if (W3C_capabilities) {
              post_json["W3C_capabilities"]["firstMatch"][0]["resetKeyboard"] = jsonResponse["desiredCapabilities"]["resetKeyboard"];
            } else {
              post_json["resetKeyboard"] = jsonResponse["desiredCapabilities"]["resetKeyboard"];
            }
          }

          post_json["enablePerformanceLogging"] = jsonResponse["desiredCapabilities"]["enablePerformanceLogging"] || false;

          if ((jsonResponse["desiredCapabilities"]["acceptSslCerts"] || "").toString() == "true")
            post_json["acceptSslCerts"] = true;
      }

      if(isNotUndefined(browserstackParams["realMobile"]) && !browserstackParams["realMobile"].toString() != "false" && jsonResponse["desiredCapabilities"]["udid"]) {
        post_json["udid"] = jsonResponse["desiredCapabilities"]["udid"];
        if(browserName.toLowerCase() === "android" || (browserName.toLowerCase() === "chrome_android")) {
          if (browserstackParams["browserstack.tunnel"] && browserstackParams["browserstack.tunnel"].toString() == "true" && lcaps["setSocksProxy"]) {
            post_json["chromeOptions"]["args"].push("--proxy-server=socks5://" + browserstackParams["browserstack.tunnelHost"] + ":" + browserstackParams["browserstack.tunnelPort"]);
          }
          else if (!doNotSetProxy) {
            // Changed from '+ 10000' to '- 12000' to bring in sync with port during firecmd. Refer 'def calculate_privoxy_port' in mobile repo.
            var localPort = parseInt(browserstackParams["appium_port"]) - 12000;

            // Not passing the following arg in case privoxy (device level proxy) is needed to be disabled
            // Refer the comments around the definition of `doNotSetProxy` variable.
            // [*** IMPORTANT ***] We are overriding this param in firecmd response. Check that too if you are making some changes here
            post_json['chromeOptions']['args'].push("--proxy-server=http://" + host_name + ":" + localPort.toString());
          }
          post_json['chromeOptions']['args'].push("--disable-features=Translate");
        }
      }

      // AI extension
      if (!isAppAutomateSession && browserstackParams["browserstack.ai_enabled_session"] && browserstackParams["browserstack.ai_enabled_session"].toString() == "true" && !post_json.realMobile){
        if (helper.isHash(post_json["chromeOptions"])) {
          if (post_json["chromeOptions"]["extensions"] && Array.isArray(post_json["chromeOptions"]["extensions"])) {
            post_json["chromeOptions"]["extensions"] = post_json["chromeOptions"]["extensions"].filter((extension) => extension != constants.AI_EXTENSIONS.chrome);
            post_json["chromeOptions"]["extensions"].push(constants.AI_EXTENSIONS.chrome);
          } else {
            post_json["chromeOptions"]["extensions"] = [constants.AI_EXTENSIONS.chrome];
          }
        }
      }

      // TODO:
      // Move All this to Rails.
      // Hub should only forward the output_capabilities generated from Rails
      if(post_json.hasOwnProperty("W3C_capabilities")) {
        var w3c_capabilities = post_json["W3C_capabilities"];
        if(helper.isHash(post_json["moz:firefoxOptions"])) {
          w3c_capabilities["firstMatch"][0]["moz:firefoxOptions"] = post_json["moz:firefoxOptions"];
        }
        if(helper.isHash(post_json["chromeOptions"])) {
          let browserOptionsKey = null;
          if(isEdgeChromium(browserName, browserVersion, true)) {
            browserOptionsKey = 'ms:edgeOptions';
          } else {
            browserOptionsKey = 'goog:chromeOptions';
          }
          w3c_capabilities["firstMatch"][0][browserOptionsKey] = helper.deepMerge(w3c_capabilities["firstMatch"][0][browserOptionsKey] || {}, post_json["chromeOptions"]);
        }
        delete post_json["W3C_capabilities"];
      }

      if(isEdgeChromium(browserName, browserVersion, true)) {
        Object.assign(post_json, {['ms:edgeOptions']: post_json['chromeOptions'] });
        const seleniumVersion = browserstackParams['browserstack.selenium.jar.version'] || browserstackParams['browserstack.selenium_version'] || undefined;
        if (isNotUndefined(seleniumVersion) && helper.validateDeleteChromeOptionsEdgeChromium(seleniumVersion)) {
          delete post_json.chromeOptions;
        }
      }

      var post_data = {
        "desiredCapabilities" : post_json
      };

      if(w3c_capabilities){
        post_data["capabilities"] = w3c_capabilities;
      }
      post_data = JSON.stringify(post_data);

      var sessionId = "";
      try {
        sessionId = browserstackParams["browserstack.aws.save"].split("/")[1];
        request.sessionId = sessionId;
      } catch(e) {
        HubLogger.miscLogger('SessionId-Failure', e.toString(), LL.WARN);
      }
      if(sessionId !== ""){
        helper.PingZombie({
          "sessionid": sessionId,
          "rails_start_time": (new Date() - railsRequestStartTime),
          "kind": (isAppAutomateSession ? "app_automation_session_stats" : "automation_session_stats"),
          "product_package": getProductPackageNameBrief(postParams.product_package),
        });
      }
      var tt = "desktop";

      var isRealMobile = (post_json["realMobile"] && post_json["realMobile"].toString() == "true")? true : false;
      if(isRealMobile) {
        tt = "realMobile";
      }
      var options = {
        request: request,
        response: response,
        post_data: post_data,
        bsCaps: lcaps,
        browserstackParams: browserstackParams,
        post_params: postParams,
        host_name: host_name,
        rproxyHost: rproxyHost,
        callback: callback,
        url: url,
        port: browserstackParams["ai_proxy_port"] || browserstackParams["appium_port"] || 5555,
        attempt: startAttempt || 1,
        mobile: lcaps["mobile"],
        rails_omitted_caps: railsOmittedCaps,
        indexCounter: indexCounter || 0,
        sessionId: sessionId,
        rails_session_id: sessionId,
        realMobile: isRealMobile,
        terminal_type: tt,
        proxy_type: browserstackParams["proxy_type"],
        automation_session_id: automation_session_id,
        build_hash: build_hash,
        collection_number: collection_number,
        user_id: session_user_id,
        group_id: session_group_id,
        group_plan_type: group_plan_type,
        privoxy_domain_control_flag: privoxy_domain_control_flag,
        desktop_telemetry_enabled: desktop_telemetry_enabled,
        desktop_telemetry_interval: desktop_telemetry_interval,
        group_risk_bucket: group_risk_bucket,
        bundle_id_block_flag: bundle_id_block_flag,
        ios_msg_logging_flag: ios_msg_logging_flag,
        iosVersion: iosVersion,
        has_edge_extension: has_edge_extension,
        shouldForceChangeJar: browserstackParams["browserstack.forceChangeJar"] || false,
        avoidAppium: avoidAppium,
        start_session_retry: 0,
        appTesting: lcaps["app"],
        originRegion: constants.region,
        prev_session_id: prevSessionId,
        request_count: 0,
        machine_key: machine_key,
        doNotSetProxy,
        hoothootCanaryTags,
        platformDetails
      };
      const isAppAutomate = postParams ? postParams.isAppAutomate : false;
      helper.pushToCLS('start_request_to_rails_got_response', {
        session_id: options.sessionId,
        user_id: options.user,
        headers: JSON.stringify(request.headers)
      }, isAppAutomate);
      browserName = (jsonResponse["desiredCapabilities"]["browser"] || jsonResponse["desiredCapabilities"]["browserName"] || "ANY").toUpperCase();
      try {
        if(avoidAppium && (browserName == "ANDROID" || browserName == "IPHONE" || browserName == "IPAD" || browserName === "APPLETV") && !options.realMobile) {
          helper.addToConsoleTimes(request, `selauth-request-end`);
          helper.addToConsoleTimes(request, 'fire-command-emulator');
          options.port = browserName == "ANDROID" ? 45693 : 3001;
          //Wait for the devices to load
          options.attempt = constants.maxAllowedDifferentMachineRetries + 1;
          options.terminal_type = "mobile";
          options.sessionId = sessionId;
          waitForEmulatorStartup(request, response, post_data, host_name, options.port, callback, options, function(request, response, post_data, host_name, port, callback, options) {
            helper.addToConsoleTimes(request, 'fire-command-emulator-end');
            startSession(request, response, post_data, host_name, options.port, callback, options);
          });
        } else {
          var callback_startSession = function(error_attempt, error_message, firecmdOptions){
            if(error_attempt){
              // Get Second terminal
              HubLogger.miscLogger("start-error-firecmd-retry", "Start Session Firecmd Error: " + error_message + "\tAttempt: " + error_attempt + "\tHost: " + host_name + "\tPort: " + options.port + "\tSessionId: " + options.sessionId, LL.WARN);
              clearInterval(options.relaxed_interval);
              var opts = _getLoggingOpts(options.bsCaps, options.browserstackParams);
              opts.indexCounter = options.indexCounter || 0;
              var pparams = options.post_params;
              pparams["hardRelease"] = host_name;
              if(options.realMobile && opts.device) pparams["hardReleaseDevice"] = opts.device;
              pparams["automation_session_id"] = options.sessionId;
              pparams["ignoreTerminalSubregionRetry"] = firecmdOptions ? (firecmdOptions.opts ? firecmdOptions.opts.ignore_terminal_on_retry : null) : null;

              //To make all data available in the opts hash
              opts["name"] = host_name;
              opts["sessionId"] = options.sessionId;

              HubLogger.seleniumStats("automate-firecmd-retry", opts, error_message, "start-error-firecmd-retry", "nodeError");
              var return_value = exports.postBrowserStack(options.url, pparams, options.request, options.response, options.callback, options.rails_omitted_caps, undefined, 1, error_attempt, undefined, e, options.indexCounter, undefined, true);
              // in case user_disconnected_before_retry the first firecmd error terminal is yet to be released
              if(return_value === "user_disconnected_before_retry") {
                // skip to final retry handling
                var previousAttemptOpts = firecmdOptions.opts;
                var previousAttemptFirecmdParsedResponse = firecmdOptions.firecmdParsedResponse;
                if(previousAttemptOpts && previousAttemptFirecmdParsedResponse) {
                  handle_final_attempt_error_fire_command(previousAttemptOpts, error_message, previousAttemptFirecmdParsedResponse);
                }
              }
            }
            else {
              const { bsCaps: { isPlaywright, playwrightAndroid, isDetox } } = options;
              request.firecmd_time = ((new Date()) - request.firecmd_time);
              // Since playwright or detox sessions do not require explicit creation of
              // session we need not send a start session request to the jar/driver.
              if (isPlaywright) {
                if(firecmdOptions.playwrightURL) {
                  const playwrightUrl = new URL(firecmdOptions.playwrightURL);
                  firecmdOptions.playwrightURL = firecmdOptions.playwrightURL.replace('127.0.0.1', options.rproxyHost).replace('localhost', options.rproxyHost);
                  if (playwrightAndroid) {
                    firecmdOptions.playwrightURL = firecmdOptions.playwrightURL.replace(playwrightUrl.port, playwrightUrl.port - constants.CDP_PLAYWRIGHT_PORT_DIFFERENCE);
                  }
                }
                const dummySeleniumResponse = {
                  statusCode: 200,
                  data: JSON.stringify({
                    state: null,
                    status: 0,
                    hcode: 1234567,
                    sessionId: uuidv4(),
                    value: {
                      ...firecmdOptions.playwrightURL && { wsURL: firecmdOptions.playwrightURL, wsHostname: host_name }
                    }
                  }),
                  headers: {}
                };

                callback(request, response, dummySeleniumResponse, host_name, undefined, options);
              } else if (isDetox) {
                  const dummySeleniumResponse = {
                  statusCode: 200,
                  data: JSON.stringify({
                    state: null,
                    status: 0,
                    hcode: 1234567,
                    sessionId: uuidv4(),
                    value: {
                      ...firecmdOptions.detoxURL && { wsURL: firecmdOptions.detoxURL.replace('127.0.0.1', options.rproxyHost), wsHostname: host_name }
                    }
                  }),
                  headers: {}
                };

                callback(request, response, dummySeleniumResponse, host_name, undefined, options);
              } else {
                helper.addToConsoleTimes(request, 'patch-start-session');
                // Patch Start Session Params acc. to firecmd response
                options.selVersion =  isNotUndefined(firecmdOptions) ? firecmdOptions.seleniumVersion : undefined;
                patchStartSession(firecmdOptions || {}, options, () => {
                  helper.addToConsoleTimes(request, 'patch-start-session-end');
                  startSession(request, response, options.post_data, host_name, options.port, callback, options);
                });
              }
            }
          };
          options.browserVersion = parseInt(browserVersion);
          options.browserName    = browserName;
          request.firecmd_time = new Date();

          helper.addToConsoleTimes(request, `selauth-request-end`);
          fireCommands(options, callback_startSession, firecmdAttempt);
        }
      } catch(e) {
        //Who cares!!?
        HubLogger.exceptionLogger("Error starting session: " + e, hostname, "/selenium/authenticate?auth="+constants.railstoken+url);
        throw e;
      }
    }
  })
  .catch(async (e) => {
    let filteredPostParams = Object.assign({}, postParams);
    filteredPostParams.password = undefined;

    helper.addToConsoleTimes(request, `selauth-request-end`);
    switch (e.type) {
      case 'ResponseError':
        HubLogger.exceptionLogger(JSON.stringify(filteredPostParams), hostname, "/selenium/authenticate", undefined, undefined, pipelineIdentifier);
        helper.sendAlerts('Error getting response from BrowserStack', url + " " + JSON.stringify(filteredPostParams));
        break;
      case 'TimeoutError':
        HubLogger.bsLogger(hostname, url, headers, undefined, undefined, pipelineIdentifier, LL.INFO);
        HubLogger.exceptionLogger(`Timeout communicating to BrowserStack, Attempt: ${retry.attempt}`, hostname, `/selenium/authenticate?auth=${constants.railstoken}${url}`);
        HubLogger.seleniumStats(`hub_to_bs_retry`, {}, `${e.toString()}; retry attempt is ${retry.attempt}`, "data: " + JSON.stringify(filteredPostParams) + "attempt: " + retry.attempt, url, "timeout-comm-bs", {isAppAutomate: isAppAutomateSession, isDetox: isDetoxSession});
        // no break here is intentional
      case 'RequestError':
        //HubLogger.bsErrorHandler(request, response, e, null, (url + " : " + JSON.stringify(headers) + " : " + JSON.stringify(post_params)));
        HubLogger.bsLogger(hostname, url, headers, undefined, undefined, pipelineIdentifier, LL.INFO);
        HubLogger.exceptionLogger(`Error communicating to BrowserStack. Exception: ${e.toString()}, Attempt: ${retry.attempt}`, hostname, `/selenium/authenticate?auth=${constants.railstoken}${url}`);
        var output = JSON.stringify(filteredPostParams);

        if (e.type == 'RequestError') {
          HubLogger.seleniumStats(`hub_to_bs_retry`, {}, `${e.toString()}; retry.attempt is ${retry.attempt}`, "data: " + output + "url: " + url, url, "err-comm-bs", {isAppAutomate: isAppAutomateSession, isDetox: isDetoxSession});
        }
        if (retry.attempt < retry.maxRetryCount && (Date.now() - retry.firstRequestToBrowserStackTime) < retry.maxRequestLife) {
          await Promise.delay(request ? retry.requestDelay : 5 * retry.requestDelay);
          exports.postBrowserStack(url, postParams, request, response, callback, railsOmittedCaps, undefined, undefined, undefined, undefined, e, undefined, retry);
        } else {
          requestToBrowserStackError(url, postParams, request, response, callback, e, retry);
        }
        break;

      default:
        HubLogger.exceptionLogger(`CHECK postBrowserStack Exception: ${e.stack.toString()}, Attempt: ${retry.attempt}`, hostname, `/selenium/authenticate?auth=${constants.railstoken}${url}`);
        throw e;
    }
  });
  return;
};

var _postBrowserStack = function(url, post_params, request, response, callback, e, rails_omitted_caps, indexCounter, firecmd_attempt, queue_id, start_attempt, retry) {
  var isAppAutomateSession = post_params.isAppAutomate;

  if(request) {
    request.start_up_request_time = new Date();
  }

  helper.addToConsoleTimes(request, `selauth-request`);

  if(post_params.callback_stop && callback) {
    constants.callback_registry[post_params.callback_stop] = callback;
    post_params.callback_stop = constants.callbackHost;
    callback = function(){
      HubLogger.miscLogger("callbackStop", "URL: " + url, LL.INFO);
    };
  }

  var headers = {
    'content-type': 'application/json; charset=utf-8',
    'accept': 'application/json'
  };

  headers['content-length'] = Buffer.byteLength(JSON.stringify(post_params), 'utf-8');

  var hostname = isAppAutomateSession ? constants.BS_APP_ENDPOINT : constants.BS_ENDPOINT;
  var postOptions = {
    method: "POST",
    path: "/selenium/authenticate?auth=" + constants.railstoken+url+((post_params && post_params.local) ? "&local=true" : "") + ((post_params && post_params.hardRelease) ? "&hrelease=true" : "") + (post_params.hardReleaseDeviceUDID ? "&hardReleaseDevice=" + post_params.hardReleaseDeviceUDID : ""),
    headers: headers,
    agent: false,
    scheme: constants.BS_SCHEME,
    hostname: hostname,
    port: ((isAppAutomateSession) ? constants.BS_APP_ENDPOINT_PORT : constants.BS_ENDPOINT_PORT),
    timeout: constants.BS_TIMEOUT + ((post_params && post_params.local) ? 25000 : 0),
    body: JSON.stringify(post_params)
  };
  requestToBrowserStack(url, post_params, request, response, callback, rails_omitted_caps, queue_id, firecmd_attempt, start_attempt, undefined, indexCounter, postOptions, headers, retry);
};

function startSession(request, response, post_data, host_name, port, callback, options) {
  if (!options.attempt) {
    options.attempt = 1;
  }
  helper.addToConsoleTimes(request, `start-command`);
  request.start_session_time = new Date();
  var temp_post_data = JSON.parse(post_data);
  if(options.appTesting) {
    delete temp_post_data["desiredCapabilities"]["app"];
    delete temp_post_data["desiredCapabilities"]["appURL"];
    delete temp_post_data["desiredCapabilities"]["otherApps"];
    delete temp_post_data["desiredCapabilities"]["override_privoxy_forward_rules"];

    if(options.bsCaps["platform"] && options.bsCaps["platform"].toLowerCase() != 'android'){
      temp_post_data["desiredCapabilities"]["bundleId"] = temp_post_data["desiredCapabilities"]["bundleID"];
    }

    if(options.bsCaps["automationName"]
      && options.bsCaps["automationName"].toLowerCase() == "youiengine"){
      temp_post_data["desiredCapabilities"]["youiEngineAppPort"] = options.bsCaps["youiengine_driver_port"];
      delete options.bsCaps["youiengine_driver_port"];
      delete temp_post_data["desiredCapabilities"]["youiengine_driver_port"];

    }
  }

  //Running and storing check here, as browserversion is deleted from caps in below step.
  const shouldForwardProxy = checkProxyForwarding(temp_post_data.capabilities);

  temp_post_data = deleteSeleniumBrowserVersionIfRequired(temp_post_data, options.selVersion || undefined);
  post_data = JSON.stringify(temp_post_data);

  var headers = {
    'content-type': 'application/json; charset=utf-8',
    'content-length': Buffer.byteLength(post_data), accept: 'application/json'
  };
  requestlib.appendBStackHostHeader(host_name, headers);
  if (!(post_data.match(/android/) && post_data.match(/-(4\.3|4\.4|5\.0)-/))) {
    headers['connection'] = 'close';
  }

  var client_ip = request.headers["cf-connecting-ip"] || request.headers['x-forwarded-for'] || request.connection.remoteAddress;

  var kind = options.appTesting ? 'app_automation_session_stats' : 'automation_session_stats';
  const keepAlive = request.headers["x-conn"] || request.headers["connection"] || "blank";
  const product_package = options.post_params ? options.post_params.product_package : '';

  let dataToSend = {
    "sessionid": options.sessionId,
    "has_keep_alive": options.appTesting ? null : keepAlive,
    "cloudflare": request.headers["cf-ray"] || "",
    "hub_request_id": request.id,
    "product_package": getProductPackageNameBrief(product_package),
    kind,
  };

  if(![PLAYWRIGHT, PUPPETEER].includes(options.post_params.framework)) {
    dataToSend["client_ip"] = client_ip;
  }

  if (kind === 'automation_session_stats'){  // is sent from railsApp for automate
    delete dataToSend.has_keep_alive;
  }
  helper.PingZombie(dataToSend);

  if (options.appTesting) {
    const product = { performance: {} };
    product.performance.keep_alive = keepAlive;
    helper.PingZombie({ sessionid: options.sessionId, kind, product });
  }

  var startOptions = {
        method: "POST",
        path: "/wd/hub/session",
        hostname: helper.getRproxy(options),
        port: port,
        headers: headers
  };

  HubLogger.miscLogger(
    "rproxy  params in startSession",
    options.sessionId + "hostname: " + helper.getRproxy(options) +  "region :" + constants.region + "browserstackParams :" + options.browserstackParams + "options.rproxyHost :" + options.rproxyHost + "options.host_name :" + options.host_name + " on port " + options.port,
    LL.INFO
  );

  const isAppAutomate = options.appTesting || false;
  helper.pushToCLS('startsession_request_jar', {
    session_id: options.sessionId,
    user_id: options.user,
    terminal: options.hostname,
    post_data: helper.sanitizeRequestCapsForCLS(post_data)
  }, isAppAutomate);
  var timeoutValue = constants.START_REQUEST_TIMEOUT;

  // Start Session Timeout for iOS NJB:  150 + 200 seconds on production.
  if (options.realMobile && parseFloat(options.iosVersion) >= 10.0) {
    timeoutValue += 200000;
  } else if (options.iosVersion == "9.1" || options.iosVersion == "7.0" || options.realMobile) {
    timeoutValue += 350000;
  }

  startOptions.body = post_data;
  startOptions.timeout = timeoutValue;
  if (options && isNotUndefined(options.browserstackParams)) {
    startOptions.timeout = validateCustomMobileStartSessionTimeout(options.realMobile, options.browserstackParams["browserstack.customMobileStartSessionTimeout"], timeoutValue);
  }
  const startOptionsClone = Object.assign({}, startOptions);
  if (temp_post_data.capabilities) {
    const { capabilities, desiredCapabilities: dcaps } = temp_post_data;
    const chromeOpts = dcaps['chromeOptions'] || dcaps['goog:chromeOptions'];
    if (chromeOpts) {
      capabilities.firstMatch[0]['goog:chromeOptions'] = chromeOpts;
    }
    if (isNotUndefined(dcaps['proxy']) && shouldForwardProxy) {
      capabilities.firstMatch[0]['proxy'] = dcaps['proxy'];
    }
    if (isNotUndefined(dcaps['automationName'])) {
      capabilities.firstMatch[0]['automationName'] = dcaps['automationName'];
    }
    const edgeOptions = dcaps['ms:edgeOptions'];
    if(edgeOptions){
      capabilities.firstMatch[0]['ms:edgeOptions'] = edgeOptions;
    }
    const ieOptions = dcaps['se:ieOptions'];
    if(ieOptions){
      capabilities.firstMatch[0]['se:ieOptions'] = ieOptions;
    }

    if (options && isNotUndefined(options.browserstackParams)) {
      const bidiEnabled = isTrueString(options.browserstackParams['browserstack.seleniumBidi']);

      if(bidiEnabled){
        capabilities.firstMatch[0]['webSocketUrl'] = true;
      }
    }

    startOptionsClone.body = JSON.stringify({ capabilities });
    headers['content-length'] = Buffer.byteLength(startOptionsClone.body);
  }

  requestlib.call(startOptionsClone)
  .then((response1) => {
    let rproxyDiffRegionRetry = checkRproxy5xxError(response1, startOptionsClone, options, 'start-session-rproxy-retry');
    if ((response1.statusCode === 500 && options.attempt <= constants.maxAllowedDifferentMachineRetries && response1.data) || (rproxyDiffRegionRetry && options.attempt <= constants.maxAllowedDifferentMachineRetries)) {
      var opts = _getLoggingOpts(options.bsCaps, options.browserstackParams);
      var pparams = options.post_params;
      var errorData;
      var returned_value;
      var isSessionRetried = false;
      try {
        errorData = JSON.parse(response1.data);
      } catch (e) {
        isSessionRetried = true;
        HubLogger.exceptionLogger('JSON Parse Error start-request for session: ' + options.sessionId + ' ,capabilities:' + startOptionsClone.body + ' ,Exception:' + e.toString() + ' data: ' + response1.data, options.hostname);
        pparams["hardRelease"] = host_name;
        pparams["hardReleaseDevice"] = opts.device;
        pparams["automation_session_id"] = options.sessionId;
        if (rproxyDiffRegionRetry) {
          pparams["ignoreTerminalSubregionRetry"] = options.browserstackParams["browserstack.terminal_sub_region"];
        }
        opts.sessionId = options.sessionId;
        opts.name = options.host_name;
        opts.user_id = options.user || options.user_id;
        HubLogger.miscLogger("Releasing host_name: " + host_name + " , device: " + opts.device);
        HubLogger.seleniumStats("start-error-different-machine-retry", opts, e.type, e.type, "start-error", "nodeError");
        returned_value = exports.postBrowserStack(options.url, pparams, options.request, options.response, options.callback, options.rails_omitted_caps, undefined, 1, undefined, options.attempt + 1, e.type, options.indexCounter, undefined, true);
        if(returned_value !== "user_disconnected_before_retry") return;
      }

      if (!isSessionRetried) {
        var errorValueHash = errorData.value ? errorData.value : {};

        if (!isUndefined(errorValueHash.message)) {
          if (helper.canRetryAppiumStartRequest(errorValueHash.message)) {
            opts = _getLoggingOpts(options.bsCaps, options.browserstackParams);

            pparams = options.post_params;
            pparams["hardRelease"] = host_name;
            if(options.realMobile && opts.device) pparams["hardReleaseDevice"] = opts.device;
            pparams["automation_session_id"] = options.sessionId;
            opts.sessionId = options.sessionId;
            opts.name = options.host_name;
            opts.user_id = options.user || options.user_id;

            HubLogger.seleniumStats("start-error-different-machine-retry", opts, errorValueHash.message, errorValueHash.message, "start-error", "nodeError");
            returned_value  = exports.postBrowserStack(options.url, pparams, options.request, options.response, options.callback, options.rails_omitted_caps, undefined, 1, undefined, options.attempt + 1, errorValueHash, options.indexCounter, undefined, true);
            // continue with non retry handling in case of user disconnected
            if(returned_value !== "user_disconnected_before_retry") return;
          }
        }
      }
    }
    helper.pushToCLS('startsession_response_jar', {
      session_id: options.sessionId,
      user_id: options.user,
      terminal: options.hostname
    }, isAppAutomate);
    request.start_session_time = ((new Date()) - request.start_session_time);
    if (request.is_app_automate_session)
      HubLogger.sendSessionLog(options, "SESSION_SETUP_TIME", helper.getDate(), JSON.stringify({"launching_app": request.start_session_time}), false);
    helper.addToConsoleTimes(request, `start-command-end`);
    healthCheck.termialSessionList.addSession(true, options.sessionId);
    callback(request, response, response1, host_name, undefined, options);
  })
  .catch((e) => {
    helper.addToConsoleTimes(request, `start-command-end`);
    switch (e.type) {
      case 'ResponseError':
        HubLogger.exceptionLogger("Error in response on getCapabilities, " + e + "\n post_params " + util.inspect(options.post_params));
        helper.sendAlerts('Error in response on getCapabilities', e + "\n post_params " + util.inspect(options.post_params));
        break;
      case 'TimeoutError':
        healthCheck.termialSessionList.addSession(false, options.sessionId);
        helper.pushToCLS('startsession_timeout_jar', {
          session_id: options.sessionId,
          user_id: options.user,
          terminal: options.hostname
        }, isAppAutomate);
      case 'RequestError': {
        HubLogger.miscLogger("start-error", "Start Session Error: " + e + " Attempt: " + options.attempt + " Host: " + host_name + " Port: " + port + " sessionId: " + options.sessionId, LL.WARN);
        helper.pushToCLS('startsession_error_jar', {
          session_id: options.sessionId,
          user_id: options.user,
          terminal: options.hostname,
          error: e.toString()
        }, isAppAutomate);
        var opts = _getLoggingOpts(options.bsCaps, options.browserstackParams);
        opts.indexCounter = options.indexCounter || 0;

        var device = options["realMobile"]? options["device"] : undefined;
        var is_apple_os = device && helper.isAppleOs(options["orig_os"]);

        if(options.start_session_retry == 0 && is_apple_os){
          options.start_session_retry = 1;
          HubLogger.miscLogger("start-error-same-machine-retry", "Start Session Same Machine Retry: " + e + "Host: " + host_name + " Port: " + port + " sessionId: " + options.sessionId, LL.INFO);

          return setTimeout(function(){
            startSession(options.request, options.response, options.post_data, options.host_name, options.port, callback, options);
          }, 5000);
        }

        var maxAllowedStartErrorRetries = constants.maxAllowedDifferentMachineRetries;
        if (options.realMobile && options.mobile && options.mobile.version && options.mobile.version.match(/iPhone 7/i)) {
          // Allowing additional retry on start error for iPhone 7 because of unlock issues
          maxAllowedStartErrorRetries = 2;
          HubLogger.miscLogger("differentMachineRetry", "Increased max different machine retry count to 2 for sessionId: " + options.sessionId, LL.INFO);
        }
        if (options.attempt <= maxAllowedStartErrorRetries) {
          var pparams = options.post_params;
          pparams["hardRelease"] = host_name;
          if(options.realMobile && opts.device) pparams["hardReleaseDevice"] = opts.device;
          pparams["automation_session_id"] = options.sessionId;

          opts.name = host_name;
          opts.sessionId = options.sessionId;

          if(options.realMobile && !options.bsCaps["device"].match(/iphone|ipad|appletv/i)) {
            // For real mobile android, we only do different terminal retry
            HubLogger.seleniumStats("browser-startup-failure-retry", opts, e.message, e.stack.toString(), "start-error", "");
          } else {
            HubLogger.seleniumStats("selenium-node-failure", opts, e.message, e.stack.toString(), "start-error", "nodeError");
          }
          helper.pushToCLS('startsession_retry_jar', {
            session_id: options.sessionId,
            user_id: options.user,
            terminal: options.hostname
          }, isAppAutomate);
          var returned_value = exports.postBrowserStack(options.url, pparams, options.request, options.response, options.callback, options.rails_omitted_caps, undefined, 1, undefined, options.attempt + 1, e, options.indexCounter, undefined, true);
          // continue with non retry handling in case of user disconnected
          if(returned_value != "user_disconnected_before_retry") break;
        }
        HubLogger.nodeErrorHandler(request, response, e, host_name + ":" + port, options.sessionId, "start-error", undefined, undefined, opts, options, undefined, function() {
          var data = options.browserstackParams["browserstack.aws.save"].split("/"); //[0]: bucket, [1]: rails id
          if(data[1]) {
            var message = (options.bsCaps["mobile"] && options.bsCaps["mobile"]["version"]) ? (options.realMobile ? ( options.bsCaps["app"] ? constants.firecmd_custom_exceptions["unknown_exception"] : "COULD NOT START MOBILE BROWSER") : "COULD NOT BOOT EMULATOR") : "BROWSER START-UP FAILURE";
            HubLogger.addStopToRawLogs(options, data[1], message, 1, true, undefined, undefined);
          }
        });
        break;
      }
      default:
        HubLogger.exceptionLogger("CHECK startSession Exception, " + e.stack.toString() + "\n post_params " + util.inspect(options.post_params));
        throw e;
    }
  });
}
exports.startSession = startSession;

const checkRproxy5xxError = (response, startOptionsClone, options, err_type = 'rproxy-retry') => {
  let diffRegionRetry = false;
  if (response.statusCode >= 500 && response.statusCode < 600 && /rproxy/.test(startOptionsClone.hostname)) {
    try {
      JSON.parse(response.data);
    } catch (e) {
      if (response.data && response.data.toLowerCase().includes("nginx")) {
        diffRegionRetry = true;
        const prod_name = options.post_params && options.post_params.isAppAutomate ? "app-automate" : "automate";
        const hhplatform = options.realMobile ? 'mobile' : 'desktop';
        HubLogger.hoothoot.emit('automate_miscellaneous_errors', 1, { event_type: err_type, platform: hhplatform, product: prod_name, hub_region: constants.region, terminal_region: options.browserstackParams["browserstack.terminal_sub_region"], error_code: response.statusCode });
      }
    }
  }
  return diffRegionRetry;
};
exports.checkRproxy5xxError = checkRproxy5xxError;

function waitForEmulatorStartup(request, response, post_data, host_name, port, callback, options, startReqCallback) {
  var mobile_params = options["mobile"];
  var mobile_post = {
      browser: mobile_params["browser"],
      version: mobile_params["version"],
      video_session_id: options.sessionId
    };
  if(typeof options.browserstackParams["browserstack.hosts"] === 'string') {
    mobile_post["new_hosts"] = options.browserstackParams["browserstack.hosts"];
  }
  if(options.bsCaps["deviceOrientation"]) {
    mobile_post["orientation"] = options.bsCaps["deviceOrientation"];
  }
  if(options.browserstackParams["browserstack.video"]) {
    mobile_post["video"] = true;
    mobile_post["video_aws_keys"]= options.browserstackParams['browserstack.video.aws.key'];
    mobile_post["video_aws_secret"] = options.browserstackParams['browserstack.video.aws.secret'];
    mobile_post["video_aws_bucket"] = options.browserstackParams['browserstack.video.aws.s3bucket'];
    mobile_post["video_aws_region"] = options.browserstackParams['browserstack.video.aws.region'];
    mobile_post["video_file"] = options.browserstackParams['browserstack.video.filename'] || "video";
    mobile_post["video_disable_watermark"] = options.browserstackParams['browserstack.video.disableWaterMark'];
  }
  if(isTrueString(options.bsCaps["new_bucketing"])){
    mobile_post["logs_new_bucketing"] = "true";
    mobile_post["logs_aws_keys"] = options.browserstackParams['browserstack.logs.aws.key'];
    mobile_post["logs_aws_secret"] = options.browserstackParams['browserstack.logs.aws.secret'];
    mobile_post["logs_aws_bucket"] = options.browserstackParams['browserstack.logs.aws.s3bucket'];
    mobile_post["logs_aws_region"] = options.browserstackParams['browserstack.logs.aws.region'];
  }
  if (options.browserstackParams["browserstack.timezone"]){
    if (constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]]){
      mobile_post["timezone"] = constants.time_zones_mapping[options.browserstackParams["browserstack.timezone"]][0];
    }
  }
  if ((options.browserstackParams["browserstack.tunnel"] == "true" || options.browserstackParams["browserstack.tunnel"] == true) && isNotUndefined(options.browserstackParams["local_params"])) {
    mobile_post["local"] = true;
    var local_params = options.browserstackParams['local_params'];
    for(var j in local_params){
      if(j == "browser" || j == "version" || j == "device") continue;
      mobile_post[j] = local_params[j];
    }
  }
  mobile_post["timeout"] = (constants.emulator_boot_timeout - constants.relaxed_start_timeout);
  mobile_post["logHost"] = constants.zombie_server;
  mobile_post["logging"] = false;
  var request_url = "http://" + host_name + ":45671/start_mobile_selenium_hub";
  HubLogger.miscLogger("Emulator-Request", HubLogger.filterAWSKeys(request_url), LL.INFO);
  var opts = _getLoggingOpts(options.bsCaps, options.browserstackParams);

  var tempRequest = http.get({url: request_url, qs: mobile_post}, function(tempResponse) {
    var tempData = "";

    tempResponse.on('data', function(chunk){
      tempData+=chunk;
    });
    tempResponse.on('error', function(err){
      HubLogger.miscLogger("Emulator-Response-Error", host_name + " : Error getting response for boot status: " + err, LL.WARN);
    });
    tempResponse.on('end', function() {
      HubLogger.miscLogger("Emulator-Response", host_name + " : Got response for boot status: " + tempData, LL.INFO);
      return startReqCallback(request, response, post_data, host_name, port, callback, options);
    });
  }).on('error', function(e) {
    HubLogger.exceptionLogger("Error communicating with the emulator node, " + e, host_name, request_url);

    opts.name = host_name;
    opts.sessionId = options.sessionId;
    HubLogger.seleniumStats("selenium-node-failure", opts, e.toString(), "Wait for Emulator error at port 45671 Error: " + e.stack.toString(), "", "nodeError");
    return startReqCallback(request, response, post_data, host_name, port, callback, options);
  });

  helper.setTimeout(tempRequest, constants.emulator_boot_timeout, function() {
    HubLogger.exceptionLogger("Error communicating with the emulator node, timeout", host_name, request_url);

    opts.name = host_name;
    opts.sessionId = options.sessionId;
    HubLogger.seleniumStats("selenium-node-failure", opts, "timeout", "Timeout after 50 secs", "", "nodeError");
    tempRequest.abort();
  });
  tempRequest.end();
}

function filterAndSendToZombie(data, post_params, options) {
  options = options || {};
  var sendOnly = ["OS/Browser combination invalid.",
                  "Platform can be one of WINDOWS, ANY, MAC, XP, and WIN8.",
                  "Cannot find terminal."];

  var error = data["error"];

  if(sendOnly.indexOf(error) > -1 ||
      (error == "Parallel limit reached" && Object.keys(constants.global_registry).length < 2)) {
    var data1 = "Desired Capabilities: " + JSON.stringify(post_params) + " \nError: " + error;

    HubLogger.seleniumStats("BS-to-hub", {}, "", data1, "", "terminal allocation error", {isAppAutomate: options.isAppAutomate, isDetox: options.isDetox});
  }
}
exports.filterAndSendToZombie = filterAndSendToZombie;

function validateCustomMobileStartSessionTimeout(realMobile, customMobileStartSessionTimeout, timeout) {
  if (realMobile && isNotUndefined(customMobileStartSessionTimeout)) {
    timeout = Math.max(parseInt(customMobileStartSessionTimeout), 5000);
  }
  return timeout;
}

exports.validateCustomMobileStartSessionTimeout = validateCustomMobileStartSessionTimeout;

function checkProxyForwarding(capabilities) {
  if (!(capabilities && capabilities.firstMatch && capabilities.firstMatch.length !== 0 && isNotUndefined(capabilities.firstMatch[0]['browserName']) && isNotUndefined(capabilities.firstMatch[0]['browserVersion']))) {
    return false;
  }
  const firstMatch = capabilities.firstMatch[0];
  const browserVersion = extractIntVersion(capabilities.firstMatch[0]['browserVersion']);
  return ['chrome'].includes(firstMatch['browserName'].toLowerCase()) && (browserVersion >= 61 && browserVersion <= 70);
}

exports.handle_error_fire_command = handle_error_fire_command;
exports.modifyEdgeCaps = modifyEdgeCaps;

/**
 * @param {*} options
 * @returns true for windows when high contrast cap is present; exception : winxp
 */
function enableHighContrast(options) {
  return ((isTrueString(options.browserstackParams['browserstack.highContrast'])
    || isTrueString(options.browserstackParams['browserstack.high_contrast']))
    && (options.bsCaps["orig_os"].includes("win")
    && options.bsCaps["orig_os"] !== "winxp"));
}
