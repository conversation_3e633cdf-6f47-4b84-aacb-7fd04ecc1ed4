/* eslint-disable max-lines-per-function, max-lines */

'use strict';

const requestlib = require('../../lib/request');
const HubLogger = require('../../log');
const constants = require('../../constants');
const bridge = require('../../bridge');
const helper = require('../../helper');
const RestAPIHandler = require('../../helpers/customSeleniumHandling/restAPIHandler');
const BasicAuthHandlerFactory = require('../../helpers/customSeleniumHandling/basicAuthHandler/basicAuthHandlerFactory');
const queryString = require('querystring');
const redact = require('../../helpers/redactor');
const WdaClient = require('../../wdaClient');
const sslHandler = require('../../helpers/customSeleniumHandling/acceptSSLHandler/mainAcceptSSLHandler');
const sendError = require('../../helpers/customSeleniumHandling/customExecutorHelper').sendError;
const instrumentAndSendError = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendError;
const sendRequestToPlatform = require('../../helpers/customSeleniumHandling/customExecutorHelper').sendRequestToPlatform;
const checkFileUploadExecutor = require('../../helpers/customSeleniumHandling/customExecutorHelper').checkFileUploadExecutor;
const { getInstrumentationData } = require('../../helpers/customSeleniumHandling/instrumentExecutor');
const instrumentAndSendExecutorResponse = require('../../helpers/customSeleniumHandling/customExecutorHelper').instrumentAndSendExecutorResponse;
const Promise = require('bluebird');
const { annotate } = require('../../helpers/customSeleniumHandling/annotator');
const { lighthouseHandler } = require('../../helpers/lighthouseHandler');
const { isUndefined, isTrueString, isString } = require('../../typeSanity');

const browserstack = require('../../browserstack');
const path = require('path');
const pubSub = require('../../pubSub');
const SeleniumClient = require('../../seleniumClient');
const getSessionDetailsHandler = require('../../helpers/customSeleniumHandling/jsExecutorHandler/getSessionDetailsHandler');
const percy = require('../../helpers/customSeleniumHandling/jsExecutorHandler/percyScreenshotHandler');
const appAlly = require('../../helpers/customSeleniumHandling/jsExecutorHandler/appAllyHandler');
const adbCommandExecutor = require('./adbCommandExecutor');
const {
  validateAllUpdateAndroidSettings,
  setLockedForDeviceSettingsFlowAndroid,
  sendUpdateSettingsRequestToPlatform
} = require('../../helpers/customSeleniumHandling/customAndroidSettingsHelper');
const { lighthouseExecutorHandler } = require('../../helpers/lighthouse/helper');

const TAG = 'CUSTOM BROWSERSTACK EXECUTOR';

const { BROWSERSTACK_EXECUTOR_PREFIX } = constants;
const LL = constants.LOG_LEVEL;

const sendSaveFileResponse = (keyObject, requestStateObj) => {
  const executorType = 'saveFile';
  // below script navigates to save file dialog and click save
  const autoitText = '{F6}#-#{TAB}#-#{ENTER}#-#';
  if (keyObject.browser !== 'internet explorer') {
    instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_SAVE_FILE.supports_only_ie);
    return;
  }
  bridge
    // eslint-disable-next-line max-len
    .autoItSendKeys(keyObject, requestStateObj, { timeout: constants.NODE_DIED_IN }, autoitText, () => {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, true);
    }, () => {
      bridge.sendErrorResponse(keyObject, requestStateObj);
      instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_SAVE_FILE.autoit_generic_error);
    });
};

// eslint-disable-next-line max-len
const terminalResponseHandler = (keyObject, requestStateObj, fileDownloadCommand, resFromTerminal) => {
  let data = {};
  const status = 0;
  HubLogger.miscLogger(TAG, `command: ${fileDownloadCommand}  response from terminal for ${JSON.stringify(resFromTerminal)}`, LL.DEBUG, keyObject.debugSession);
  if (resFromTerminal && resFromTerminal.data) {
    if (fileDownloadCommand !== 'fileExists' && resFromTerminal.statusCode === 404) {
      instrumentAndSendError(fileDownloadCommand, keyObject, requestStateObj, constants.JSE_FILE_EXECUTORS_GENERIC.file_not_found);
      return;
    }
    switch (fileDownloadCommand) {
      case 'uploadFile':
      case 'fileExists':
        data = JSON.parse(resFromTerminal.data).success;
        break;
      case 'getFileProperties':
        data = JSON.parse(resFromTerminal.data).data;
        break;
      case 'getFileContent':
        data = resFromTerminal.buffer.toString('base64');
        break;
      default:
        HubLogger.miscLogger(TAG, `invalid action found while parsing terminal response: ${fileDownloadCommand}`);
        instrumentAndSendError(fileDownloadCommand, keyObject, requestStateObj, constants.JSE_FILE_EXECUTORS_GENERIC.invalid_action);
        return;
    }
  } else {
    HubLogger.miscLogger(TAG, 'undefined response from terminal', LL.INFO);
    instrumentAndSendError(fileDownloadCommand, keyObject, requestStateObj, constants.JSE_FILE_EXECUTORS_GENERIC.generic_error);
    return;
  }
  requestStateObj.hash = 'POST:value';
  requestStateObj.data = JSON.stringify({
    sessionId: requestStateObj.clientSessionID,
    status,
    value: data,
  });
  instrumentAndSendExecutorResponse(fileDownloadCommand, keyObject, requestStateObj, false);
};

// eslint-disable-next-line max-len
const doRemoteFileActions = (keyObject, requestStateObj, endpointForTerminal, fileDownloadCommand) => {
  HubLogger.miscLogger(TAG, `hitting terminal endpoint: ${endpointForTerminal}`);
  const options = {
    method: 'GET',
    hostname: keyObject.rproxyHost,
    port: helper.getPortForDesktopOS(keyObject),
    path: endpointForTerminal,
    headers: requestlib.appendBStackHostHeader(keyObject.name),
    timeout: constants.JS_EXECUTOR_TIMEOUT,
    recordJarTime: true
  };

  requestlib.call(options).then((res) => {
    helper.addToJarTime(keyObject.rails_session_id, res);

    HubLogger.miscLogger(TAG, `doRemoteFileActions request for sessionId: ${keyObject.rails_session_id}`, LL.DEBUG);
    terminalResponseHandler(keyObject, requestStateObj, fileDownloadCommand, res);
  }).catch((_err) => {
    HubLogger.miscLogger(TAG, `doRemoteFileActions failed for sessionId: ${keyObject.rails_session_id} with error: ${_err}`, LL.INFO);
    instrumentAndSendError(fileDownloadCommand, keyObject, requestStateObj, constants.JSE_FILE_EXECUTORS_GENERIC.generic_error);
  });
};

const checkFileOperationsExecutor = (keyObject) => {
  if (keyObject.terminal_type !== 'desktop') {
    return constants.JSE_FILE_EXECUTORS_GENERIC.supports_only_desktop;
  }
  return undefined;
};

const runGenericFileExecutor = (keyObject, requestStateObj, parsedCommand, endpointForTerminal) => {
  let encodedFileName;
  if (parsedCommand.arguments && parsedCommand.arguments.fileName) {
    encodedFileName = encodeURIComponent(parsedCommand.arguments.fileName);
  }
  const queryData = queryString.encode({
    file_name: encodedFileName,
    allowed_file_size: constants.allowedFileSizeBytes,
    browser_version: keyObject.browser_version, // to send browser version to terminal
  });
  endpointForTerminal = `/${endpointForTerminal}?${queryData}`;
  const { action } = parsedCommand;
  doRemoteFileActions(keyObject, requestStateObj, endpointForTerminal, action);
};

const runGetFileContentExecutor = (keyObject, requestStateObj, parsedCommand) => {
  const error = checkFileOperationsExecutor(keyObject);
  if (error) {
    instrumentAndSendError(parsedCommand.action, keyObject, requestStateObj, error);
    return;
  }
  runGenericFileExecutor(keyObject, requestStateObj, parsedCommand, 'get_file_content');
};

const runUploadFileExecutor = (keyObject, requestStateObj, parsedCommand) => {
  // To click on "open" button in file explorer window using autoIt script
  const error = checkFileUploadExecutor(keyObject);
  HubLogger.miscLogger('runUploadFileExecutor', `processing uploadFile executor ${parsedCommand} for session ${keyObject.rails_session_id} error ${error}`);
  if (error) {
    instrumentAndSendError(parsedCommand.action, keyObject, requestStateObj, error);
    return;
  }
  runGenericFileExecutor(keyObject, requestStateObj, parsedCommand, 'upload_file');
};

const runGetFilePropertiesExecutor = (keyObject, requestStateObj, parsedCommand) => {
  const error = checkFileOperationsExecutor(keyObject);
  if (error) {
    instrumentAndSendError(parsedCommand.action, keyObject, requestStateObj, error);
    return;
  }
  runGenericFileExecutor(keyObject, requestStateObj, parsedCommand, 'get_file_properties');
};

const runFileExistsExecutor = (keyObject, requestStateObj, parsedCommand) => {
  const error = checkFileOperationsExecutor(keyObject);
  if (error) {
    instrumentAndSendError(parsedCommand.action, keyObject, requestStateObj, error);
    return;
  }
  runGenericFileExecutor(keyObject, requestStateObj, parsedCommand, 'check_file_present');
};

const runBasicAuthExecutor = (keyObject, requestStateObj, parsedCommand) => {
  // eslint-disable-next-line max-len
  const basicAuthHandler = BasicAuthHandlerFactory.getHandler(keyObject, requestStateObj, parsedCommand);
  basicAuthHandler.executeBasicAuth();
};

const runDisimissBasicAuthExecutor = (keyObject, requestStateObj, parsedCommand) => {
  // eslint-disable-next-line max-len
  const basicAuthHandler = BasicAuthHandlerFactory.getHandler(keyObject, requestStateObj, parsedCommand);
  basicAuthHandler.executeDismissBasicAuth();
};

const runUpdateSessionStatusHandler = (keyObject, requestStateObj, parsedCommand) => {
  const { status, action } = parsedCommand.arguments;
  if (!status) {
    instrumentAndSendError(action, keyObject, requestStateObj, constants.JSE_SESSION_STATUS.missing_status);
    return;
  }
  const apiHandler = new RestAPIHandler(keyObject.rails_session_id, keyObject.appTesting);
  apiHandler.runUpdateSessionStateExecutor(keyObject, requestStateObj, parsedCommand);
};

const runUpdateSessionNameHandler = (keyObject, requestStateObj, parsedCommand) => {
  const apiHandler = new RestAPIHandler(keyObject.rails_session_id, keyObject.appTesting);
  apiHandler.runUpdateSessionStateExecutor(keyObject, requestStateObj, parsedCommand);
};

const handleAndroidBiometricPopupHandler = async (keyObject, requestStateObj, biometricMatch) => {
  const executorType = 'biometric_custom_executor';
  const errors = constants.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS;
  const sel = new SeleniumClient(keyObject);

  try {
    const error = await sel.handleBiometricPopupAndroid(biometricMatch);
    if (error == null) {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
    } else {
      instrumentAndSendError(executorType, keyObject, requestStateObj, error);
    }
  } catch (err) {
    HubLogger.tempExceptionLogger(`Error in Biometric popup handler for session: ${keyObject.rails_session_id}`, err);
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.unknown);
  }
};

const updateAndroidDeviceSettings = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'android_device_settings_executor';

  const hourFormatErrors = constants.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS;
  const dateTimeErrors = constants.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS;
  const androidSettingsErrors = constants.UPDATE_ANDROID_SETTINGS_EXECUTOR_ERRORS;
  const args = parsedCommand.arguments;
  let requestData = null;

  const customObject = {
    date: null,
    time: null,
    dateTimeErrors,
    error: null,
    executorError: null,
    hourFormat12: null
  };

  if (keyObject.isSmartTV) {
    customObject.error = constants.JSE_GENERIC_ERRORS.smart_tv_not_supported;
  } else if (keyObject.isLockedForDeviceSettingsFlow) {
    customObject.error = androidSettingsErrors.parallel_update_android_settings_command_error;
  } else if (!keyObject.appTesting) {
    customObject.error = dateTimeErrors.incompatible_product;
  } else if (keyObject.os === 'ios') {
    customObject.error = dateTimeErrors.incompatible_platform;
  } else {
    const listOfAndroidSettingsToBeUpdated = new Set();

    validateAllUpdateAndroidSettings(keyObject,
      args,
      dateTimeErrors,
      hourFormatErrors,
      customObject,
      listOfAndroidSettingsToBeUpdated);

    if (customObject.date != null || customObject.time != null || customObject.hourFormat12 != null) {
      requestData = {
        updateAndroidSettings: [...listOfAndroidSettingsToBeUpdated],
        date_time: {
          date: customObject.date,
          time: customObject.time,
          hourFormat12: customObject.hourFormat12
        }
      };
    }
  }

  if (customObject.error !== null) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      customObject.error
    );
    return;
  }

  if (requestData === null) {
    HubLogger.miscLogger('updateAndroidSettings', 'Dropping request due to no valid command present');
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      androidSettingsErrors.invalid_argument
    );
    return;
  }

  // Checking for lock again to avoid race conditions
  if (!keyObject.isLockedForDeviceSettingsFlow) {
    setLockedForDeviceSettingsFlowAndroid(keyObject, true);
  } else {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      androidSettingsErrors.parallel_update_android_settings_command_error
    );
    return;
  }
  sendUpdateSettingsRequestToPlatform(keyObject, requestData, requestStateObj, executorType, dateTimeErrors, customObject);
};

const handleiOSBiometricPopupHandler = async (keyObject, requestStateObj, biometricMatch) => {
  const executorType = 'biometric_custom_executor';
  const errors = constants.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS;
  const wda = new WdaClient(keyObject.name, keyObject.wda_port, keyObject.rproxyHost);
  let previousTryError;

  for (let tries = 0; tries <= 5; tries += 1) {
    try {
      // eslint-disable-next-line no-await-in-loop
      await wda.attach();
      let elementId;
      if (biometricMatch === 'pass') {
        biometricMatch = 'Pass';
      } else if (biometricMatch === 'cancel') {
        biometricMatch = 'Cancel';
      } else {
        biometricMatch = 'Fail';
      }
      try {
        // eslint-disable-next-line no-await-in-loop
        elementId = await wda.findElement('accessibility id', biometricMatch);
      } catch (err) {
        // no element is found using the selectors accessibility id would retry for max 5 times
        previousTryError = errors.popup_absent;
        // eslint-disable-next-line no-continue
        continue;
      }
      try {
        // eslint-disable-next-line no-await-in-loop
        await wda.clickElement(elementId);
      } catch (err) {
        // element is found but not clicked
        previousTryError = errors.popup_not_clicked;
        // eslint-disable-next-line no-continue
        continue;
      }
      previousTryError = null;
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
      return;
    } catch (err) {
      const stackTrace = err.stack ? err.stack.toString() : err.toString();

      HubLogger.tempExceptionLogger(`Exception in Biometric pop up handler session: ${keyObject.rails_session_id}`, stackTrace);
      // eslint-disable-next-line no-await-in-loop
      await Promise.delay(2000);
    }
  }
  const error = previousTryError || errors.unknown;
  instrumentAndSendError(executorType, keyObject, requestStateObj, error);
};

// check for image extensions only  [".jpg", ".jpeg", ".png"] allowed
const checkImgExtension = fileName => constants.IMAGE_INJECTION_SUPPORTED_IMAGES
  .some(supportedExt => path.extname(fileName) === supportedExt);

const checkVideoExtension = fileName => constants.VIDEO_INJECTION_SUPPORTED_VIDEOS
  .some(supportedExt => path.extname(fileName) === supportedExt);

const checkAudioExtension = (fileName, product = 'app_automate', os = 'android') => constants.AUDIO_INJECTION_SUPPORTED_FORMATS[product][os]
  .some(supportedExt => path.extname(fileName).toLowerCase() === supportedExt);

/* eslint-disable complexity */
const validateCameraAndAudioInjection =
  (keyObject, requestStateObj, parsedCommand, executorType, executorSelector, errors, mediaArgsDynamic) => {
    if ((!keyObject.enableCameraImageInjection && executorSelector === 'camera') || (!keyObject.enableCameraVideoInjection && executorSelector === 'video') || (!keyObject.enableAudioInjection && executorSelector === 'audio')) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.invalid_action_used
      );
      return false;
    }

    if (!keyObject.appTesting && (executorSelector === 'camera' || executorSelector === 'video')) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.invalid_action_name
      );
      return false;
    }

    if (executorSelector === 'audio' && keyObject.os !== 'android') {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.platform_not_supported
      );
      return false;
    }

    const mediaArguments = parsedCommand.arguments;


    if ((!helper.nestedKeyValue(mediaArguments, [mediaArgsDynamic]) ||
      !helper.isDefined(mediaArguments[mediaArgsDynamic]) ||
      !(typeof mediaArguments[mediaArgsDynamic] === 'string' ||
        mediaArguments[mediaArgsDynamic] instanceof String))) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.invalid_argument_passed
      );
      return false;
    }

    if (!mediaArguments[mediaArgsDynamic].match(/media:\/\/[a-zA-Z0-9]+$/)) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        executorSelector === 'camera' ? errors.invalid_image_url : errors.invalid_media_url
      );
      return false;
    }
    return true;
  };

const validateMediaObjectAndSendRequestToPlatform = (response, keyObject, requestStateObj, executorSelector, errors, executorType, mediaArguments, mediaArgsDynamic) => {
  const browserstackResponse = JSON.parse(response);
  let mediaObj = null;
  const product = keyObject.appTesting ? 'app_automate' : 'automate';
  if (browserstackResponse.resolve_media === 'success') {
    mediaObj = JSON.parse(browserstackResponse.media_obj);
    if (!isUndefined(mediaObj)) { // We receive mediaObj as null if media_id is not present
      const fileName = mediaObj.filename;
      let isValidExt = false;
      switch (executorSelector) {
        case 'camera':
          isValidExt = checkImgExtension(fileName);
          break;
        case 'video':
          isValidExt = checkVideoExtension(fileName);
          break;
        case 'audio':
          isValidExt = checkAudioExtension(fileName, product, keyObject.os);
          break;
        default:
          HubLogger.miscLogger(TAG, `invalid executorSelector found while validating mediaobject: ${executorSelector}`);
      }
      if (!isValidExt) {
        if (executorSelector === 'camera' || executorSelector === 'video') {
          instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_image_type);
        } else {
          instrumentAndSendError(executorType, keyObject, requestStateObj, keyObject.appTesting ? errors.invalid_media_type_aa : errors.invalid_media_type_aut);
        }
        return false;
      }
    } else {
      instrumentAndSendError(executorType, keyObject, requestStateObj, executorSelector === 'camera' ? errors.invalid_image_url : errors.invalid_media_url);
      return false;
    }
  } else { // We receive "resolve_media" => failed whenever something broke on rails side
    instrumentAndSendError(executorType, keyObject, requestStateObj, (executorSelector === 'camera' || executorSelector === 'video') ? errors.unknown : errors.audio_injection_failure);
    return false;
  }

  const baseUrl = (executorSelector === 'camera' || executorSelector === 'video') ? '/inject_image' : '/inject_audio';

  // At this point all validations have passed, we can send request to platform now
  let serverURL = `${baseUrl}?device=${encodeURIComponent(keyObject.device)}&session_id=${encodeURIComponent(keyObject.rails_session_id)}&media_hashed_id=${encodeURIComponent(mediaArguments[mediaArgsDynamic].split('media://')[1])}&format=${encodeURIComponent(path.extname(mediaObj.filename))}&product=${encodeURIComponent(product)}&file_url=${encodeURIComponent(mediaObj.s3_url)}`;
  if (keyObject.os.toLowerCase() === 'ios' && !isUndefined(keyObject.bundlesForMultiAppImageInjection)) {
    serverURL = serverURL.concat(`&bundles_for_multi_app_image_injection=${encodeURIComponent(keyObject.bundlesForMultiAppImageInjection)}`);
  }
  const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

  sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, (executorSelector === 'camera' || executorSelector === 'video') ? errors.unknown : errors.audio_injection_failure, customTimeout);

  return true;
};

const runCameraImageAndAudioInjectionHandler = (keyObject, requestStateObj, parsedCommand, executorSelector) => {
  let executorType = 'cameraImageInjection_custom_executor';
  let errors = constants.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS;
  let mediaArgsDynamic = 'imageUrl';

  if (executorSelector === 'audio') {
    executorType = 'injectAudio';
    errors = constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS;
    mediaArgsDynamic = 'audioUrl';
  }

  if (executorSelector === 'video') {
    executorType = 'injectVideo';
    errors = constants.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS;
    mediaArgsDynamic = 'videoUrl';
  }

  try {
    if (keyObject.isSmartTV) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      return;
    }
    const mediaArguments = parsedCommand.arguments;
    if (!validateCameraAndAudioInjection(keyObject, requestStateObj, parsedCommand, executorType, executorSelector, errors, mediaArgsDynamic)) {
      return;
    }

    let mediaArgs = null;
    switch (executorSelector) {
      case 'camera':
        mediaArgs = mediaArguments.imageUrl;
        break;
      case 'video':
        mediaArgs = mediaArguments.videoUrl;
        break;
      case 'audio':
        mediaArgs = mediaArguments.audioUrl;
        break;
      default:
        HubLogger.miscLogger(TAG, `invalid executorSelector found while parsing executorSelector: ${executorSelector}`);
    }

    const bsUrl = `&resolve_media=true&media_id=${mediaArgs}&user_id=${keyObject.user}&access_key=${keyObject.accesskey}`;

    browserstack.postBrowserStack(bsUrl, {
      isAppAutomate: !!keyObject.appTesting,
      automation_session_id: keyObject.rails_session_id,
    }, null, null, (response) => {
      HubLogger.miscLogger(TAG, `resolve_media: postBrowserStack response ${JSON.stringify(response)}`, LL.INFO);
      validateMediaObjectAndSendRequestToPlatform(response, keyObject, requestStateObj, executorSelector, errors, executorType, mediaArguments, mediaArgsDynamic);
    });
  } catch (e) {
    HubLogger.tempExceptionLogger('Camera and Audio Injection Handler: Error in runCameraImageAndAudioInjectionHandler', e);
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.unknown);
  }
};
/* eslint-enable complexity */

const runControlAudioHandler = (keyObject, requestStateObj, audioControl) => {
  const errors = constants.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS;
  const executorType = audioControl === 'start' ? 'startAudio' : 'stopAudio';
  const mobileUrlBase = audioControl === 'start' ? 'play_audio' : 'stop_audio';

  try {
    if (keyObject.isSmartTV) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      return;
    } else if (!keyObject.enableAudioInjection) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.invalid_action_used
      );
      return;
    }

    if (keyObject.os !== 'android') {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.platform_not_supported
      );
      return;
    }

    const product = keyObject.appTesting ? 'app_automate' : 'automate';
    const serverURL = `/${mobileUrlBase}?device=${encodeURIComponent(keyObject.device)}&session_id=${encodeURIComponent(keyObject.rails_session_id)}&product=${encodeURIComponent(product)}`;
    const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

    const responseHandler = (responseCallBack) => {
      if (responseCallBack && responseCallBack.statusCode === 200) {
        instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
        return;
      } if (responseCallBack && responseCallBack.statusCode !== 200) {
        try {
          const data = responseCallBack.data;
          const parsedData = JSON.parse(data);
          if (parsedData && parsedData.code === 'AL004') {
            instrumentAndSendError(executorType,
              keyObject,
              requestStateObj,
              errors.audio_start_custom_failure);
            return;
          } if (parsedData && parsedData.code === 'AL005') {
            instrumentAndSendError(executorType,
              keyObject,
              requestStateObj,
              errors.audio_stop_custom_failure);
            return;
          }
        } catch (err) {
          HubLogger.tempExceptionLogger('Audio Injection Handler: Error in parsing respone from platform', err);
        }
      }
      instrumentAndSendError(executorType, keyObject, requestStateObj, executorType === 'startAudio' ? errors.audio_start_failure : errors.audio_stop_failure);
    };
    sendRequestToPlatform(executorType,
      serverURL, requestStateObj,
      keyObject,
      errors.unknown,
      customTimeout,
      null,
      responseHandler);
  } catch (e) {
    HubLogger.tempExceptionLogger('Audio Injection Handler: Error in runControlAudioHandler', e);

    instrumentAndSendError(executorType,
      keyObject,
      requestStateObj,
      errors.unknown);
  }
};

const customGesturesHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'customGestures_custom_executor';
  const customGesturesErrors = constants.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS;
  const { deviceShake } = parsedCommand.arguments;

  let error = false;

  const setLockedForCustomGestureFlow = (value) => {
    keyObject.isLockedForCustomGestureFlow = value;
    // makes lock persistent in case for hub toggle
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        isLockedForCustomGestureFlow: value,
      },
    });
  };

  if (keyObject.isSmartTV) {
    error = constants.JSE_GENERIC_ERRORS.smart_tv_not_supported;
  } else if (keyObject.isLockedForCustomGestureFlow) {
    error = customGesturesErrors.parallel_custom_gestures_command_error;
  } else if (!keyObject.appTesting) {
    error = customGesturesErrors.incompatible_product;
  } else if (keyObject.os.toLowerCase() === 'android') {
    error = customGesturesErrors.incompatible_platform;
  } else if (parseInt(keyObject.os_version, 10) < 16 || parseInt(keyObject.os_version, 10) >= 26) {
    error = customGesturesErrors.incompatible_os_version;
  } else if (isUndefined(deviceShake) || !(deviceShake === 'true')) {
    error = customGesturesErrors.incorrect_syntax;
  } else if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.DEVICE_SHAKE_CODE)) {
    error = customGesturesErrors.feature_not_available_in_current_plan_for_aa;
  }

  if (error !== false) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      error
    );
    return;
  }

  // Checking for lock again to avoid race conditions
  if (!keyObject.isLockedForCustomGestureFlow) {
    setLockedForCustomGestureFlow(true);
  } else {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      customGesturesErrors.parallel_custom_gestures_command_error
    );
    return;
  }

  const serverURL = `/shake?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    app_automate_session_id: keyObject.rails_session_id,
    product: 'app_automate',
  }));

  const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

  const customOptions = {
    method: 'POST',
    body,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': body.length,
    }
  };

  const responseHandler = (response) => {
    setLockedForCustomGestureFlow(false);

    if (response && response.statusCode === 200) {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
    } else {
      HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
      instrumentAndSendError(executorType, keyObject, requestStateObj, customGesturesErrors.executor_internal_error);
    }
  };

  await sendRequestToPlatform(executorType,
    serverURL, requestStateObj,
    keyObject,
    customGesturesErrors.executor_internal_error,
    customTimeout,
    customOptions, responseHandler);
};

const applePayCustomHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'applePay_custom_executor';
  const applePayErrors = constants.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS;
  const args = parsedCommand.arguments;

  let error = false;

  const setLockedForApplePayFlow = (value) => {
    keyObject.isLockedForApplePayFlow = value;
    // makes lock persistent in case for hub toggle
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        isLockedForApplePayFlow: value,
      },
    });
  };

  if (keyObject.isSmartTV) {
    error = constants.JSE_GENERIC_ERRORS.smart_tv_not_supported;
  } else if (!isTrueString(keyObject.enableApplePay) && !isTrueString(keyObject.is_dedicated_cloud_session)) {
    error = applePayErrors.caps_not_passed;
  } else if (!Object.prototype.hasOwnProperty.call(args, 'confirmPayment')) {
    error = applePayErrors.invalid_args;
  } else if (!isTrueString(args.confirmPayment)) {
    error = applePayErrors.invalid_value;
  } else if (keyObject.isLockedForApplePayFlow) {
    error = applePayErrors.parallel_apple_pay_command_error;
  }

  if (error !== false) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      error
    );
    return;
  }

  // Checking for lock again to avoid race conditions
  if (!keyObject.isLockedForApplePayFlow) {
    setLockedForApplePayFlow(true);
  } else {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      applePayErrors.parallel_apple_pay_command_error
    );
    return;
  }

  const serverURL = `/execute_apple_pay?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    session_id: keyObject.rails_session_id,
    product: keyObject.appTesting ? 'app_automate' : 'automate',
  }));

  const customTimeout = 40000;

  const customOptions = {
    method: 'POST',
    body,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': body.length,
    }
  };

  const responseHandler = (response) => {
    setLockedForApplePayFlow(false);

    if (response && response.statusCode === 200) {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
    } else {
      HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
      instrumentAndSendError(executorType, keyObject, requestStateObj, applePayErrors.executor_internal_error);
    }
  };

  await sendRequestToPlatform(executorType,
    serverURL, requestStateObj,
    keyObject,
    applePayErrors.executor_internal_error,
    customTimeout,
    customOptions, responseHandler);
};

const validateApplePayPrefillDetail = (keyObject, args) => {
  const applePayPrefillDataErrors = constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS;

  if (keyObject.isSmartTV) {
    return constants.JSE_GENERIC_ERRORS.smart_tv_not_supported;
  }

  if (!isTrueString(keyObject.enableApplePay)) {
    return applePayPrefillDataErrors.caps_not_passed;
  }

  if (keyObject.isLockedForApplePayPrefillDetailFlow) {
    return applePayPrefillDataErrors.parallel_apple_pay_commands_error;
  }

  const applePayPrefillDataValidKeys = Object.keys(constants.APPLE_PAY_PREFILL_DATA_SCHEMA);
  const isApplePayPrefillDataKeysValid = Object.keys(args).every(key => applePayPrefillDataValidKeys.includes(key));

  if (!isApplePayPrefillDataKeysValid) {
    return applePayPrefillDataErrors.invalid_argument;
  }

  if (args.billingDetails) {
    if (typeof args.billingDetails !== 'object') {
      return applePayPrefillDataErrors.invalid_argument;
    }

    const billingAddressValidKeys = Object.keys(constants.APPLE_PAY_PREFILL_DATA_SCHEMA.billingDetails);
    const isBillingAddressKeysValid = Object.keys(args.billingDetails).every(key => billingAddressValidKeys.includes(key) && (typeof args.billingDetails[key] === 'string'));

    if (!isBillingAddressKeysValid) {
      return applePayPrefillDataErrors.invalid_argument;
    }
  }

  if (args.shippingDetails) {
    if (typeof args.shippingDetails !== 'object') {
      return applePayPrefillDataErrors.invalid_argument;
    }

    const shippingAddressValidKeys = Object.keys(constants.APPLE_PAY_PREFILL_DATA_SCHEMA.shippingDetails);
    const isShippingAddressKeysValid = Object.keys(args.shippingDetails).every(key => shippingAddressValidKeys.includes(key) && (typeof args.shippingDetails[key] === 'string'));

    if (!isShippingAddressKeysValid) {
      return applePayPrefillDataErrors.invalid_argument;
    }
  }

  if (args.contact) {
    if (typeof args.contact !== 'object') {
      return applePayPrefillDataErrors.invalid_argument;
    }

    const contactValidKeys = Object.keys(constants.APPLE_PAY_PREFILL_DATA_SCHEMA.contact);
    const isContactAddressKeysValid = Object.keys(args.contact).every(key => contactValidKeys.includes(key) && (typeof args.contact[key] === 'string'));

    if (!isContactAddressKeysValid) {
      return applePayPrefillDataErrors.invalid_argument;
    }
  }

  return null;
};

const applePayPrefillDetailHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'applePayDetails';
  const applePayPrefillDataErrors = constants.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS;

  const setLockedForApplePayPrefillDetailFlow = (value) => {
    keyObject.isLockedForApplePayPrefillDetailFlow = value;
    // makes lock persistent in case for hub toggle
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        isLockedForApplePayPrefillDetailFlow: value,
      },
    });
  };

  try {
    const args = parsedCommand.arguments;
    const error = validateApplePayPrefillDetail(keyObject, args);

    if (error) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        error
      );
      return;
    }

    // Checking for lock again to avoid race conditions
    if (!keyObject.isLockedForApplePayPrefillDetailFlow) {
      setLockedForApplePayPrefillDetailFlow(true);
    } else {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        applePayPrefillDataErrors.parallel_apple_pay_commands_error
      );
      return;
    }

    const serverURL = `/prefill_apple_pay_detail?device=${encodeURIComponent(keyObject.device)}`;
    const body = Buffer.from(JSON.stringify({
      session_id: keyObject.rails_session_id,
      product: keyObject.appTesting ? 'app_automate' : 'automate',
      applePayDetails: args
    }));

    const customTimeout = 120 * 1000;

    const customOptions = {
      method: 'POST',
      body,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json; charset=utf-8',
        'content-length': body.length,
      }
    };

    const responseHandler = (response) => {
      setLockedForApplePayPrefillDetailFlow(false);

      if (response && response.statusCode === 200) {
        instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
      } else {
        HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
        try {
          const data = response.data;
          const parsedData = JSON.parse(data);
          if (parsedData && parsedData.code === 'APPD_0001') {
            instrumentAndSendError(executorType,
              keyObject,
              requestStateObj,
              applePayPrefillDataErrors.missing_argument);
            return;
          }
        } catch (err) {
          HubLogger.tempExceptionLogger('Apple Pay Prefill Data Handler: Error in parsing respone from platform', err);
        }
        instrumentAndSendError(executorType, keyObject, requestStateObj, applePayPrefillDataErrors.internal_error);
      }
    };

    sendRequestToPlatform(executorType,
      serverURL, requestStateObj,
      keyObject,
      applePayPrefillDataErrors.internal_error,
      customTimeout,
      customOptions, responseHandler);
  } catch (e) {
    setLockedForApplePayPrefillDetailFlow(false);
    HubLogger.tempExceptionLogger('Error in Apple Pay Prefill Data Handler', e);
    instrumentAndSendError(executorType,
      keyObject,
      requestStateObj,
      applePayPrefillDataErrors.internal_error);
  }
};

/* eslint-disable complexity */
const runUpdateAppSettingsHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'app_setting_custom_executor';
  const errors = constants.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS;
  const customConstants = constants.SETTINGS_APP_CUSTOM_EXECUTOR;

  // Update App Setting Validations
  // Check for App Automate Session
  if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.APP_SETTINGS)) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.feature_not_available_in_current_plan_for_aa);
    return;
  }
  if (!keyObject.appTesting) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_action_name);
    return;
  } else if (keyObject.os && keyObject.os.toLowerCase() !== 'ios') {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_os);
    return;
  }

  // Check argument format
  const settingsArguments = parsedCommand.arguments;
  if (!helper.isDefined(settingsArguments) || !helper.isHash(settingsArguments) || Object.keys(settingsArguments).length === 0) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.invalid_argument_passed,
    );
    return;
  }

  // Check app contains settings bundle
  if (!keyObject.isAppSettingsBundlePresent && JSON.stringify(Object.keys(settingsArguments)) !== JSON.stringify(['Permission Settings'])) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.no_settings_bundle,
    );
    return;
  }

  // Check number of times executor is called
  const instruData = getInstrumentationData(keyObject);
  if (instruData && instruData.custom_executor && instruData.custom_executor[executorType]) {
    if (instruData.custom_executor[executorType].count > customConstants.max_count) {
      instrumentAndSendError(
        executorType,
        keyObject,
        requestStateObj,
        errors.max_num_times,
      );
      return;
    }
  }

  // Check number of keys in DSL passed
  if (helper.getCountOfKeys(settingsArguments, 0, customConstants.max_keys) > customConstants.max_keys) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      errors.max_key,
    );
    return;
  }

  const setLockedForAppSettingsFlow = (value) => {
    keyObject.isLockedForAppSettingsFlow = value;
    // makes lock persistent in case for hub toggle
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        isLockedForAppSettingsFlow: value,
      },
    });
  };

  // setting lock to prevent other parallel requests while settings app automation is being executed
  setLockedForAppSettingsFlow(true);

  const serverURL = `/update_app_settings?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    device: keyObject.device,
    user_id: keyObject.user_id,
    app_display_name: keyObject.appDisplayName,
    session_id: keyObject.rails_session_id,
    product: 'app_automate',
    update_app_settings: settingsArguments,
  }));

  const customTimeout = customConstants.timeout * 1000;
  const customOptions = {
    method: 'POST',
    body,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': body.length,
    }
  };

  const customCallback = () => {
    // unlock to allow other requests to be processed
    setLockedForAppSettingsFlow(false);
  };

  /*
   expected response data:
   {
     "status": "error/success",
     "error": { // optional key only in case of error
       "kind" : "",
       :meta_data => {  "key" : "", "value" : "" }
      }
   }
  */
  const responseHandler = (response) => {
    if (response && response.statusCode === 200) {
      try {
        const data = response.data;
        const parsedData = JSON.parse(data);
        if (parsedData.status === 'success') {
          instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
          return;
        } if (parsedData.status === 'error' && parsedData.error) {
          const kind = parsedData.error.kind;
          const metaData = parsedData.error.meta_data;
          const errorObject = { ...errors[kind] };
          if (metaData) errorObject.message = helper.stringTemplating(errorObject.message, metaData);
          instrumentAndSendError(executorType, keyObject, requestStateObj, errorObject);
          return;
        }
      } catch (err) {
        HubLogger.tempExceptionLogger('UpdateAppSettingsHandler: Error in parsing respone from platform', err);
      }
    }
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.internal_error);
  };

  sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, errors.internal_error, customTimeout, customOptions, responseHandler, customCallback);
};

const runBiometricPopupHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'biometric_custom_executor';
  const errors = constants.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS;

  if (!keyObject.appTesting) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_action_name);
    return;
  } else if (keyObject.isSmartTV) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
    return;
  } else if (!keyObject.enableBiometric) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, constants.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS.invalid_action_used);
    return;
  }

  const { biometricMatch } = parsedCommand.arguments;
  if (isUndefined(biometricMatch) || !(biometricMatch === 'pass' || biometricMatch === 'fail' || biometricMatch === 'cancel')) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_arg_passed);
    return;
  }

  if (keyObject.os === 'android') {
    await handleAndroidBiometricPopupHandler(keyObject, requestStateObj, biometricMatch);
  } else {
    await handleiOSBiometricPopupHandler(keyObject, requestStateObj, biometricMatch);
  }
};

const deviceInfoHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'device_info_executor';
  const errors = constants.DEVICE_INFO_CUSTOM_EXECUTOR_ERRORS;
  const args = parsedCommand.arguments;
  const hash = 'POST:value';

  if (!keyObject.enableSim) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.caps_not_passed);
    return;
  } if (!(keyObject.enableSim && Object.keys(args).length === 1 && args.deviceProperties !== undefined && args.deviceProperties !== null && args.deviceProperties.length === 1 && args.deviceProperties[0] === 'simOptions')) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_syntax);
    return;
  } if (keyObject.simPhoneNumber == null || keyObject.simRegion == null) {
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.executor_internal_error);
    return;
  }

  const data = {
    'Phone Number': keyObject.simPhoneNumber,
    Region: keyObject.simRegion
  };

  if (keyObject.esim) {
    data.esim = true;
  }

  instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, true, hash, data);
};

const updateIosDeviceSettings = async (keyObject, requestStateObj, parsedCommand) => {
  const dateTimeErrors = constants.DATE_TIME_CUSTOM_EXECUTOR_ERRORS;
  const locationServicesErrors = constants.LOCATION_SERVICES_CUSTOM_EXECUTOR_ERRORS;
  const hourFormatErrors = constants.HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS;
  const iosSettingsErrors = constants.IOS_SETTINGS_EXECUTOR_ERRORS;
  const args = parsedCommand.arguments;
  const validKeys = ['customTime', 'LocationServices', '12HourTime', 'customDate'];
  const executorType = Object.keys(args).length > 0 && validKeys.includes(Object.keys(args)[0]) ? `ios_device_settings_executor_${Object.keys(args)[0]}` : 'ios_device_settings_executor';

  let error = null;
  let requestData = null;
  let executorError = null;

  const setLockedForDeviceSettingsFlow = (value) => {
    keyObject.isLockedForDeviceSettingsFlow = value;
    // makes lock persistent in case for hub toggle
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: {
        isLockedForDeviceSettingsFlow: value,
      },
    });
  };

  if (keyObject.isSmartTV) {
    error = constants.JSE_GENERIC_ERRORS.smart_tv_not_supported;
  } else if (keyObject.isLockedForDeviceSettingsFlow) {
    error = iosSettingsErrors.parallel_update_ios_settings_command_error;
  } else if (args.customDate && !keyObject.appTesting) {
    error = dateTimeErrors.incompatible_product;
  } else if (!keyObject.appTesting) {
    error = iosSettingsErrors.incompatible_product;
  } else if (keyObject.os === 'android') {
    error = iosSettingsErrors.incompatible_platform;
  } else {
    // Parse only 1 command because platform code can only handle one command per request
    /* eslint-disable complexity */
    Object.keys(args).slice(0, 1).forEach((key) => {
      switch (key) {
        case 'customTime': {
          if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE)) {
            error = dateTimeErrors.feature_not_available_in_current_plan_for_aa;
            break;
          }
          if (parseInt(keyObject.os_version, 10) < 13) {
            error = dateTimeErrors.incompatible_os_version;
            break;
          }
          const givenTime = args.customTime;
          if (!/^(2[0-3]|[01]?[0-9]):([0-5][0-9])$/.test(givenTime)) {
            error = dateTimeErrors.invalid_value;
            break;
          }
          requestData = {
            ios_setting: 'date_time',
            date_time: {
              type: 'time',
              time: givenTime
            }
          };
          executorError = dateTimeErrors.executor_internal_error;
          break;
        }
        case 'LocationServices': {
          if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_LOCATION_SERVICES_PLAN_CODE)) {
            error = locationServicesErrors.feature_not_available_in_current_plan_for_aa;
            break;
          }

          if (parseInt(keyObject.os_version, 10) < 15) {
            error = locationServicesErrors.incompatible_os_version;
            break;
          }

          const givenValue = args.LocationServices;
          if (!['Off', 'On', 'off', 'on', 'OFF', 'ON'].includes(givenValue)) {
            error = locationServicesErrors.invalid_value;
            break;
          }
          requestData = {
            ios_setting: 'location_services',
            value: givenValue,
            app_display_name: keyObject.appDisplayName
          };
          executorError = locationServicesErrors.executor_internal_error;
          break;
        }
        case 'customDate': {
          if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_DATE_PLAN_CODE)) {
            error = dateTimeErrors.feature_not_available_in_current_plan_for_aa;
            break;
          }
          if (!keyObject.aaDeviceDateAccessible) {
            error = dateTimeErrors.invalid_action_by_group;
            break;
          }
          if (parseInt(keyObject.os_version, 10) < 16) {
            error = dateTimeErrors.incompatible_custom_date_os_version;
            break;
          }
          const givenDate = args.customDate;
          const parsedDate = new Date(givenDate);
          const dateRegex = new RegExp('^[A-Za-z]{3} [0-9]{1,2} [0-9]{4}$');
          // 3 checks, 1. Check if getTime gives NaN 2. Regex to check format as JS accepts 'FEB -1 2023', 'FEB 01 202$' as valid date
          // 3. Check if date object's date is same as passed by user. If user passes 'FEB 30 2023', it gets range over to 2nd March 2023. parsedDate.getDate() returns 2 for such case
          const invalidDate = Number.isNaN(parsedDate.getTime()) || !dateRegex.test(givenDate) || parsedDate.getDate() !== +givenDate.split(' ')[1];
          if (invalidDate) {
            error = dateTimeErrors.invalid_custom_date_value;
            break;
          }

          requestData = {
            ios_setting: 'date_time',
            date_time: {
              type: 'date',
              date: {
                day: parsedDate.getDate(),
                month: parsedDate.getMonth() + 1, // January in js is 0, December is 11
                year: parsedDate.getFullYear()
              }
            }
          };
          executorError = dateTimeErrors.custom_date_executor_internal_error;
          break;
        }
        case '12HourTime': {
          if (!keyObject.accessibleFeaturesList.includes(constants.DF_FEATURE_FENCE_CODES.CUSTOM_TIME_PLAN_CODE)) {
            error = hourFormatErrors.feature_not_available_in_current_plan_for_aa;
            break;
          }
          if (parseInt(keyObject.os_version, 10) < 15) {
            error = hourFormatErrors.incompatible_os_version;
            break;
          }
          const givenValue = args['12HourTime'];
          if (!givenValue || !['on', 'off'].includes(givenValue.toLowerCase())) {
            error = hourFormatErrors.invalid_value;
            break;
          }
          requestData = {
            ios_setting: 'date_time',
            date_time: {
              type: '12HourTime',
              value: givenValue.toLowerCase() === 'on'
            }
          };
          executorError = hourFormatErrors.executor_internal_error;
          break;
        }
        default:
          break;
      }
    });
  }

  if (error !== null) {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      error
    );
    return;
  }

  if (requestData === null) {
    HubLogger.miscLogger('updateIosDeviceSettings', 'Dropping request due to no valid command present');
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      iosSettingsErrors.invalid_argument
    );
    return;
  }

  // Checking for lock again to avoid race conditions
  if (!keyObject.isLockedForDeviceSettingsFlow) {
    setLockedForDeviceSettingsFlow(true);
  } else {
    instrumentAndSendError(
      executorType,
      keyObject,
      requestStateObj,
      iosSettingsErrors.parallel_update_ios_settings_command_error
    );
    return;
  }

  const serverURL = `/set_ios_settings?device=${encodeURIComponent(keyObject.device)}`;
  const body = Buffer.from(JSON.stringify({
    device: keyObject.device,
    app_automate_session_id: keyObject.rails_session_id,
    product: 'app_automate',
    user_id: keyObject.user,
    ...requestData
  }));

  const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

  const customOptions = {
    method: 'POST',
    body,
    headers: {
      accept: 'application/json',
      'content-type': 'application/json; charset=utf-8',
      'content-length': body.length,
    }
  };

  const responseHandler = (response) => {
    setLockedForDeviceSettingsFlow(false);

    if (response && response.statusCode === 200) {
      instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
    } else {
      HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
      try {
        const data = response.data;
        const parsedData = JSON.parse(data);
        if (parsedData && parsedData.error_code && constants.DF_EXECUTOR_ERROR_CODES[parsedData.error_code]) {
          executorError = constants.DF_EXECUTOR_ERROR_CODES[parsedData.error_code];
        }
      } catch (err) {
        HubLogger.tempExceptionLogger('JS Excecutor Error: Error in parsing respone from platform', err);
      }
      instrumentAndSendError(executorType, keyObject, requestStateObj, executorError);
    }
  };

  sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, executorError, customTimeout, customOptions, responseHandler);
};

const biometricUserOptionHandler = async (keyObject, requestStateObj, parsedCommand) => {
  const executorType = 'biometric_user_option_custom_executor';
  const errors = constants.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS;
  try {
    if (!keyObject.appTesting) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_action_name);
      return;
    }
    if (keyObject.isSmartTV) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.smart_tv_not_supported);
      return;
    }
    if (!keyObject.enableBiometric) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_action_used);
      return;
    }
    if (keyObject.os !== 'ios') {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.incompatible_os_version);
      return;
    }

    const biometricUserOption = parsedCommand.arguments.userOption;
    HubLogger.miscLogger(TAG, `biometricUserOption from user ${biometricUserOption} ${biometricUserOption === 'pass'}`, LL.INFO);
    if (isUndefined(biometricUserOption) || !(['pass', 'fail', 'cancel'].includes(biometricUserOption))) {
      instrumentAndSendError(executorType, keyObject, requestStateObj, errors.invalid_arg_passed);
      return;
    }

    const serverURL = `/biometric_user_option?device=${encodeURIComponent(keyObject.device)}`;
    const body = Buffer.from(JSON.stringify({
      device: keyObject.device,
      app_automate_session_id: keyObject.rails_session_id,
      product: 'app_automate',
      biometric_user_option: biometricUserOption
    }));

    // need to discuss its need currently keeping it standard used here
    const customTimeout = (keyObject.idle_timeout - (keyObject.idle_timeout > 20 ? 20 : 0)) * 1000;

    const customOptions = {
      method: 'POST',
      body,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json; charset=utf-8',
        'content-length': body.length,
      }
    };

    const responseHandler = (response) => {
      if (response && response.statusCode === 200) {
        instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj);
      } else {
        HubLogger.tempExceptionLogger(`Non 200 response from platform for session: ${keyObject.rails_session_id}`);
        instrumentAndSendError(executorType, keyObject, requestStateObj, errors.executor_internal_error);
      }
    };

    sendRequestToPlatform(executorType, serverURL, requestStateObj, keyObject, errors.executor_internal_error, customTimeout, customOptions, responseHandler);
  } catch (exception) {
    HubLogger.tempExceptionLogger('Exception caught in BiometricUserOptionHandler', exception);
    instrumentAndSendError(executorType, keyObject, requestStateObj, errors.executor_internal_error);
  }
};

/* eslint-disable complexity */
const runBstackCommand = (commandJsonString, parsedCommand, keyObject, requestStateObj) => {
  const { action } = parsedCommand;

  // Basic Auth command is not logged here, as it contains sensitive data
  if (action !== 'sendBasicAuth') {
    HubLogger.miscLogger(TAG, `parsedCommand: ${parsedCommand}`, LL.INFO);
  }

  switch (action) {
    case 'fileExists':
      runFileExistsExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'getFileProperties':
      runGetFilePropertiesExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'getFileContent':
      runGetFileContentExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'uploadFile':
      runUploadFileExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'saveFile':
      sendSaveFileResponse(keyObject, requestStateObj);
      return;
    case 'sendBasicAuth':
      if (parsedCommand) {
        const redactedCommand = `${BROWSERSTACK_EXECUTOR_PREFIX} ${redact.redactCustomExecutorData(commandJsonString, parsedCommand)}`;
        if (requestStateObj.request) requestStateObj.request.log_data = JSON.stringify({ script: redactedCommand, args: [] });

        HubLogger.miscLogger(TAG, `parsedCommand: ${redactedCommand}`, LL.INFO);
      }
      runBasicAuthExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'dismissBasicAuth':
      runDisimissBasicAuthExecutor(keyObject, requestStateObj, parsedCommand);
      return;
    case 'lighthouse':
      lighthouseHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'lighthouseAudit':
      lighthouseExecutorHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'acceptSsl':
      sslHandler.runAcceptSSLHandler(keyObject, requestStateObj);
      return;
    case 'setSessionStatus':
      runUpdateSessionStatusHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'setSessionName':
      runUpdateSessionNameHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'annotate':
      annotate(keyObject, requestStateObj, parsedCommand);
      return;
    case 'biometric':
      runBiometricPopupHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'cameraImageInjection':
      runCameraImageAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'camera');
      return;
    case 'cameraVideoInjection':
      runCameraImageAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'video');
      return;
    case 'injectAudio':
      runCameraImageAndAudioInjectionHandler(keyObject, requestStateObj, parsedCommand, 'audio');
      return;
    case 'startAudio':
      runControlAudioHandler(keyObject, requestStateObj, 'start');
      return;
    case 'stopAudio':
      runControlAudioHandler(keyObject, requestStateObj, 'stop');
      return;
    case 'updateAppSettings':
      runUpdateAppSettingsHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'getSessionDetails':
      getSessionDetailsHandler.runGetSessionDetailsHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'adbShell':
      adbCommandExecutor.executeAdbCommand(keyObject, requestStateObj, parsedCommand);
      return;
    case 'updateAndroidDeviceSettings':
      updateAndroidDeviceSettings(keyObject, requestStateObj, parsedCommand);
      return;
    case 'percyScreenshot':
      percy.percyScreenshotHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'appAllyScan':
      appAlly.appAllyScreenshotHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'deviceInfo':
      deviceInfoHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'updateIosDeviceSettings':
      updateIosDeviceSettings(keyObject, requestStateObj, parsedCommand);
      return;
    case 'customGesture':
      customGesturesHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'applePay':
      applePayCustomHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'applePayDetails':
      applePayPrefillDetailHandler(keyObject, requestStateObj, parsedCommand);
      return;
    case 'biometricUserOption':
      biometricUserOptionHandler(keyObject, requestStateObj, parsedCommand);
      return;
    default:
      instrumentAndSendError(action, keyObject, requestStateObj, constants.JSE_GENERIC_ERRORS.invalid_action);
  }
};

const parseAndRunBstackCommand = (commandJsonString, keyObject, requestStateObj) => {
  let parsedCommand;
  let action;
  let errorMessage;
  try {
    parsedCommand = JSON.parse(commandJsonString);
    action = parsedCommand.action;
  } catch (e) {
    errorMessage = 'Invalid command format. JSON parsing failed';
  }
  if (!action) {
    errorMessage = 'Invalid command format : action is a mandatory key';
  }

  if (errorMessage) {
    HubLogger.miscLogger(TAG, `Error in browserstack_executor command: ${errorMessage} ${commandJsonString}`, LL.INFO);
    sendError(keyObject, requestStateObj, errorMessage);
    return;
  }
  runBstackCommand(commandJsonString, parsedCommand, keyObject, requestStateObj);
};

function checkBstackExecutorString(script) {
  return script && typeof script === 'string' && script.startsWith(BROWSERSTACK_EXECUTOR_PREFIX);
}

const checkandExecuteIfBstackExecutor = (keyObject, requestStateObj) => {
  const reqData = requestStateObj.req_data;
  if (!reqData) {
    return false;
  }

  if (reqData.includes(BROWSERSTACK_EXECUTOR_PREFIX)) {
    const script = JSON.parse(reqData).script;
    if (isString(script)) {
      const commandJsonString = script.split(BROWSERSTACK_EXECUTOR_PREFIX)[1];
      try {
        parseAndRunBstackCommand(commandJsonString, keyObject, requestStateObj);
      } catch (exception) {
        HubLogger.tempExceptionLogger('Error in browserstack_executor', exception);
        sendError(keyObject, requestStateObj, 'Something went wrong');
      }
      return true;
    }
  }
  return false;
};

function isBStackExecutor(request) {
  if (request.method.toUpperCase() !== 'POST' || !request.url.match(/\/wd\/hub\/session\/[^/]+\/execute\/sync$/)) {
    return false;
  }
  try {
    const script = JSON.parse(request.log_data).script;
    return checkBstackExecutorString(script);
  } catch (err) {
    return false;
  }
}

module.exports = {
  checkBstackExecutorString,
  checkandExecuteIfBstackExecutor,
  isBStackExecutor,
};
