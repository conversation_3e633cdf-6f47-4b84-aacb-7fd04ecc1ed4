# BrowserStack Executor Commands: Quick Reference Guide

## Problem Summary

**Issue**: Apple Pay executor commands fail in Playwright sessions with `BROWSERSTACK_APPLE_PAY_EXECUTOR_ERROR`

**Root Cause**: Async callback response handling incompatibility in `sendRequestToPlatform` for CDP sessions

**Impact**: Only executor commands that use `sendRequestToPlatform` with async callbacks fail in Playwright sessions

**Working Commands**: `setSessionStatus`, `setSessionName`, `annotate` (use direct API calls)
**Failing Commands**: `applePay`, `applePayDetails`, file operations (use async platform callbacks)

## Technical Root Cause

### Working Commands Flow (✅ Both Selenium & Playwright)
```
Client → Handler → Direct API Call → Manual Response Construction → instrumentAndSendExecutorResponse → Success
```

### Failing Commands Flow (❌ Playwright Only)
```
Client → Handler → sendRequestToPlatform → Async Callback → instrumentAndSendExecutorResponse → ❌ FAILS in CDP sessions
```

### Working vs Failing Examples
**✅ Working (setSessionStatus):**
```javascript
// Direct response construction
requestStateObj.data = JSON.stringify({...});
instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj, false);
```

**❌ Failing (applePay):**
```javascript
// Async callback pattern
const responseHandler = (response) => {
  instrumentAndSendExecutorResponse(executorType, keyObject, requestStateObj); // Fails in CDP
};
await sendRequestToPlatform(..., responseHandler);
```

## Solution Overview

### Key Changes Required

1. **Enhanced Response Helper** (`helpers/customSeleniumHandling/customExecutorHelper.js`)
   - Add CDP session detection
   - Implement `onResolve` callback handling
   - Maintain HTTP compatibility

2. **Apple Pay Handler Update** (`lib/customSeleniumHandler/jsExecutor.js`)
   - Add CDP-specific response handling
   - Implement proper error formatting

3. **Playwright Context Enhancement** (`socketManagers/playwrightHandler.js`)
   - Provide complete session context
   - Ensure proper session mapping

## Implementation Checklist

### Phase 1: Core Infrastructure
- [ ] Add `const { isCDP } = require('../../socketManagers/validations');` to customExecutorHelper.js
- [ ] Update `instrumentAndSendExecutorResponse` function
- [ ] Update `instrumentAndSendError` function
- [ ] Add unit tests

### Phase 2: Apple Pay Handler
- [ ] Add CDP detection to jsExecutor.js
- [ ] Update `responseHandler` in `applePayCustomHandler`
- [ ] Add error handling for CDP sessions
- [ ] Test Apple Pay specific flows

### Phase 3: Playwright Handler
- [ ] Update `requestStateObj` in both entry points
- [ ] Add `clientSessionID`, `hostname`, `rproxyHost` to context
- [ ] Test end-to-end flows

### Phase 4: Testing
- [ ] Unit tests for CDP response handling
- [ ] Integration tests for Playwright executor commands
- [ ] Regression tests for Selenium compatibility
- [ ] Performance validation

## Code Snippets

### Enhanced Response Handler
```javascript
const instrumentAndSendExecutorResponse = (executorType, keyObject, requestStateObj, 
  generateDefaultResponseHashAndData = true, hash = 'POST:keys', output = null) => {
  setInstrumentationData(keyObject, executorType, 'success');
  
  // Enhanced CDP session handling
  if (isCDP(keyObject) && requestStateObj.onResolve) {
    const responseData = {
      sessionId: requestStateObj.clientSessionID || keyObject.rails_session_id,
      status: 0,
      value: output || null
    };
    requestStateObj.onResolve(JSON.stringify(responseData));
    return;
  }
  
  sendExecutorResponse(requestStateObj, keyObject, generateDefaultResponseHashAndData, hash, output);
};
```

### Enhanced Error Handler
```javascript
const instrumentAndSendError = (executorType, keyObject, requestStateObj, error) => {
  setInstrumentationData(keyObject, executorType, 'error', error.code);
  const errorMessage = error.message.replace(constants.JSE_EXECUTERTYPE_REGEX, executorType);
  
  // Enhanced CDP session error handling
  if (isCDP(keyObject) && requestStateObj.onResolve) {
    const errorResponse = {
      sessionId: requestStateObj.clientSessionID || keyObject.rails_session_id,
      status: 13,
      value: {
        error: 'unknown error',
        message: errorMessage
      }
    };
    requestStateObj.onResolve(JSON.stringify(errorResponse));
    return;
  }
  
  sendError(keyObject, requestStateObj, errorMessage, false);
};
```

### Enhanced Playwright Context
```javascript
const requestStateObj = {
  req_data: JSON.stringify({
    script: executorString.value || evalString
  }),
  onResolve: resolve,
  // Enhanced session context
  clientSessionID: registry.rails_session_id,
  hostname: registry.name,
  rproxyHost: registry.rproxyHost
};
```

## Testing Commands

```bash
# Unit tests
npm test -- --grep "BrowserStack Executor CDP Support"

# Integration tests  
npm run test:integration -- --grep "Playwright Executor Commands"

# Full regression
npm test
```

## Success Criteria

- ✅ Apple Pay executor works in Playwright sessions
- ✅ All executor commands work across session types
- ✅ Selenium sessions remain unaffected
- ✅ Proper error messages displayed
- ✅ No performance degradation

## Risk Assessment

**Low Risk**: Additive changes, backward compatible, targeted fixes

**Mitigation**: Comprehensive testing, gradual rollout, monitoring, rollback plan

## Files Modified

1. `helpers/customSeleniumHandling/customExecutorHelper.js` (~30 lines)
2. `lib/customSeleniumHandler/jsExecutor.js` (~50 lines)  
3. `socketManagers/playwrightHandler.js` (~15 lines)
4. New test files (~500 lines total)

## Timeline

- **Week 1**: Core infrastructure + unit tests
- **Week 2**: Apple Pay integration + integration tests  
- **Week 3**: Playwright handler updates + comprehensive testing
- **Week 4**: Validation + documentation

## Next Steps

1. Review and approve implementation plan
2. Create feature branch and implement changes
3. Execute testing strategy
4. Deploy with monitoring
5. Update documentation and training materials
