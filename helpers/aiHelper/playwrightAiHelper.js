'use strict';

const constants = require('../../constants');
const logger = require('../../logger').basicLogger;
const { global_registry: globalRegistry } = require('../../constants');
const { isNotUndefined, isHash, isUndefined, isTrueString } = require('../../typeSanity');
const { promisify } = require('util');
const sleep = promisify(setTimeout);
const AICommandHelper = require('../../controllers/seleniumCommand/helpers/AICommandHelper');
const pubSub = require('../../pubSub');

const TAG = 'PlaywrightAICommandHandler';

const BASE_HEALING_REQUEST_ID_VALUE = 100000;
const WAIT_FOR_METHOD = 'waitForSelector';
const RAILS_SESSION_ID = 'rails_session_id';
const kOriginalRequest = Symbol('originalRequest');
const kHealingRequestId = Symbol('healingRequestId');
const kWaitForRequestSuccess = Symbol('waitForRequestSuccess');
const kCurrentHealingStatus = Symbol('healingStatus');
const kSelfHeal = Symbol('selfHeal');
const kSoftHeal = Symbol('softHeal');
const kHealedSelector = Symbol('healedSelector');
const kHealingStartTime = Symbol('healingStartTime');
const kHealingEndTime = Symbol('healingEndTime');

// Define symbols for healing status
const HEALING_STATUS = {
  WAIT: Symbol('waitForSelector'),
  EXECUTE_SCRIPT: Symbol('executeScript'),
  HEALED_SELECTOR: Symbol('healedSelector'),
  ORIGINAL_SELECTOR: Symbol('originalSelector')
};

const { TCG_ENDPOINTS, TCG_HEADERS, PLAYWRIGHT_SELECTOR_MAP } = require('../../controllers/seleniumCommand/helpers/AICommandHelper');

/**
 * Represents a handler for AI commands in SeleniumHub.
 * Steps for healing:
 * 1. waitFor request for the selector request
 * 2. Execute Script to trigger AI extension event based on the waitFor request's response
 * 3. If waitFor request is success - Make the original request with original selector
 * 4. If waitFor request is failure - Get healed selector and Make the original request with healed selector
 * 5. If healed selector request is failure - Make the original request with original selector
 */
class SocketAiHandler {
  constructor() {
    // Map to store session data with socket_id as key
    this.sessionData = new Map();
    this.aiSessions = new Map();
  }

  addAiEnabledSession(socket_id, session_id) {
    try {
      if (this.aiSessions.has(socket_id))
        return;
      const sessionKeyObj = this.getRegistryObject(session_id);
      logger.debug(sessionKeyObj.aiSessionDetails, { TAG })
      if (sessionKeyObj && sessionKeyObj.isPlaywright && (isTrueString(sessionKeyObj.aiSessionDetails.enable_ai_healing) || isTrueString(sessionKeyObj.aiSessionDetails.ai_soft_heal))) {
        const aiType = isTrueString(sessionKeyObj.aiSessionDetails.enable_ai_healing) ? kSelfHeal : kSoftHeal;
        this.aiSessions.set(socket_id, aiType);
        this.setSessionData(socket_id, RAILS_SESSION_ID, session_id);

        // Make sure rails_session_id is set on sessionKeyObj for proper metric tracking
        if (!sessionKeyObj.rails_session_id) {
          sessionKeyObj.rails_session_id = session_id;
          logger.debug(`Setting rails_session_id to ${session_id} on sessionKeyObj`, { TAG });
        }

        // Initialize Playwright AI metrics
        if (!sessionKeyObj.playwrightAiMetrics) {
          sessionKeyObj.playwrightAiMetrics = {
            cumulativeHealingTime: 0
          };
        }

        // Initialize ai_healing_details with all required fields
        if (!sessionKeyObj.ai_healing_details) {
          sessionKeyObj.ai_healing_details = {
            total_healing_duration: 0,
            healingTime: 0,
            total_healing_enabled_request: 0,
            total_healing_request: 0,
            script_exec_error_count: 0,
            pre_check_failure_count: 0,
            healing_failure_count: 0,
            healing_success_count: 0
          };
          logger.debug(`Initialized ai_healing_details with zero values`, { TAG });
        }

        // Increment total_healing_enabled_request counter
        if ('total_healing_enabled_request' in sessionKeyObj.ai_healing_details) {
          sessionKeyObj.ai_healing_details.total_healing_enabled_request += 1;
          logger.debug(`Incremented total_healing_enabled_request to ${sessionKeyObj.ai_healing_details.total_healing_enabled_request}`, { TAG, socketId: socket_id });
        }

        logger.debug(`Added AI enabled session: ${socket_id}`, { TAG });
      }
    } catch (error) {
      logger.error(`Error adding AI enabled session: ${error.message}`, { TAG });
    }
  }

  /**
   * Gets session data for the specified key
   * @param {string} socket_id - Session ID
   * @param {string} key - Key to retrieve
   * @returns {*} - Value stored for the key or undefined
   */
  getSessionData(socket_id, key) {
    if (!this.sessionData.has(socket_id)) {
      return undefined;
    }

    const sessionMap = this.sessionData.get(socket_id);
    if (sessionMap.has(key)) {
      return sessionMap.get(key);
    }

    return undefined;
  }

  /**
   * Sets session data for the specified key
   * @param {string} socket_id - Session ID
   * @param {string} key - Key to set
   * @param {*} value - Value to store
   */
  setSessionData(socket_id, key, value) {
    if (!this.sessionData.has(socket_id)) {
      this.sessionData.set(socket_id, new Map());
    }
    const sessionMap = this.sessionData.get(socket_id);
    sessionMap.set(key, value);
    logger.debug(`Set session data for ${key.toString()}`, { TAG, socket_id });
  }

  /**
   * Gets keyobject from global registry
   * @param {string} sessionId - Session ID
   */
  getRegistryObject(sessionId) {
    if (isUndefined(sessionId)) {
      throw new Error(`socketId is undefined`);
    }
    return globalRegistry[sessionId];
  }

  /**
   * Clears session state and data
   * @param {string} socketId - Session ID
   */
  clearSessionState(socketId) {
    // Push final healing metrics before clearing state
    try {
      const sessionID = this.getSessionData(socketId, RAILS_SESSION_ID);
      if (sessionID) {
        const keyObject = this.getRegistryObject(sessionID);
        if (keyObject) {
          const keyObjectDiffHash = {};

          // Ensure healing status values are correct before session end
          if (keyObject.selfHealingSuccess === 'true') {
            keyObject.selfHealingSuccess = 'true';
            keyObjectDiffHash.selfHealingSuccess = 'true';
          } else if (keyObject.softHealingSuccess === 'true') {
            keyObject.softHealingSuccess = 'true';
            keyObjectDiffHash.softHealingSuccess = 'true';
          }

          // Process metrics
          if (keyObject.playwrightAiMetrics) {
            const { cumulativeHealingTime = 0 } = keyObject.playwrightAiMetrics;
            logger.debug(`Final healing metrics - Time: ${cumulativeHealingTime}ms`, { TAG, socketId: sessionID });

            // Ensure data is set in ai_healing_details directly for helper.js to use
            if (!keyObject.ai_healing_details) {
              keyObject.ai_healing_details = {};
            }
            keyObject.ai_healing_details.total_healing_duration = cumulativeHealingTime;
            keyObjectDiffHash.ai_healing_details = keyObject.ai_healing_details;

            logger.debug(`Final healing metrics - Total: ${cumulativeHealingTime}ms`, { TAG, socketId: sessionID });
          }

          // Publish final updates
          if (Object.keys(keyObjectDiffHash).length > 0) {
            logger.debug(`Publishing final status updates before clearing session`, {
              TAG,
              socketId,
              selfHealingSuccess: keyObject.selfHealingSuccess,
              softHealingSuccess: keyObject.softHealingSuccess
            });

            pubSub.publish(constants.updateKeyObject, {
              session: sessionID,
              changed: keyObjectDiffHash
            });
          }

        }
      }
    } catch (error) {
      logger.error(`Error pushing final healing metrics: ${error.message}`, { TAG });
    }

    // Clear session data
    this.sessionData.delete(socketId);
    this.aiSessions.delete(socketId);
    logger.debug(`Cleared session state for ${socketId}`, { TAG });
  }

  /**
   * Checks if a request can be healed
   * @param {Object} request - Request object
   * @returns {boolean} - Whether the request is healable
   */
  isRequestHealable(request) {
    const healableMethods = ['fill', 'selectOption', 'click'];
    const method = request?.method;
    const selector = request?.params?.selector ?? '';
    const isValidSelector = typeof selector === 'string' && !selector.includes('>>');
    const isHealable = healableMethods.includes(method) && isValidSelector;

    logger.debug(`Request healability check: ${isHealable}`, {
      TAG,
      method,
      selector: selector.substring(0, 50) // truncate long selectors
    });
    return isHealable;
  }

  /**
   * Checks if a response is a healing response
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response object
   * @returns {boolean} - Whether the response is a healing response
   */
  isHealingResponse(socket_id, response) {
    if (!this.aiSessions.has(socket_id)) {
      return false;
    }
    logger.debug(`Evaluating healing response`, { TAG, socket_id });

    if (!response?.id) {
      logger.debug(`Missing or invalid response.id`, { TAG, socket_id });
      return false;
    }

    const originalRequest = this.getSessionData(socket_id, kOriginalRequest);
    if (!originalRequest?.id) {
      logger.debug(`Missing originalRequest.id`, { TAG, socket_id });
      return false;
    }
    logger.debug(`originalRequset.id: ${JSON.stringify(originalRequest)} :: ${JSON.stringify(response)}`, { TAG, socket_id });
    if (response.id >= BASE_HEALING_REQUEST_ID_VALUE || originalRequest.id === response.id) {
      return true;
    }
    return false;
  }

  /**
   * Gets a new unique request ID for healing requests
   * @param {string} socket_id - Session ID
   * @returns {number} - New request ID
   */
  getNewHealingRequestId(socket_id) {
    let newRequestId = this.getSessionData(socket_id, kHealingRequestId);
    if (newRequestId) {
      newRequestId = newRequestId + 1;
    } else {
      newRequestId = BASE_HEALING_REQUEST_ID_VALUE;
    }
    this.setSessionData(socket_id, kHealingRequestId, newRequestId);
    logger.debug(`Generated new healing request ID: ${newRequestId}`, { TAG, socket_id });
    return newRequestId;
  }

  /**
   * Checks if a Playwright request was successful
   * @param {Object} response - Response object
   * @returns {boolean} - Whether the request was successful
   */
  isPlaywrightRequestSuccess(response) {
    return !(response.error && response.log);
  }

  /**
   * Creates a waitFor request to verify if the element is available
   * @param {string} socket_id - Session ID
   * @param {Object} request - Original request object
   * @returns {Object} - New request or original request if not healable
   */
  waitForRequest(socket_id, request) {
    logger.debug(`Processing waitForRequest`, { TAG, socket_id });

    if (!this.aiSessions.has(socket_id) || !(this.isRequestHealable(request) || request.id >= BASE_HEALING_REQUEST_ID_VALUE)) {
      return request;
    }
    logger.debug(`Request healable`, { TAG, socket_id });

    // Start healing time tracking
    this.setSessionData(socket_id, kHealingStartTime, Date.now());

    const originalRequestCopy = JSON.parse(JSON.stringify(request));
    this.setSessionData(socket_id, kOriginalRequest, originalRequestCopy);
    this.setSessionData(socket_id, kCurrentHealingStatus, HEALING_STATUS.WAIT);
    const newRequest = {
      ...request,
      id: this.getNewHealingRequestId(socket_id),
      method: WAIT_FOR_METHOD
    };

    logger.debug(`Created new waitFor request`, { TAG, socket_id });
    if (isNotUndefined(newRequest?.metadata?.apiName)) {
      newRequest.metadata.apiName = 'locator.waitFor';
    }
    return newRequest;
  }

  /**
   * Creates a request to trigger event for AI extension
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response from waitFor request
   * @param {Object} handler - Handler to send requests to terminal
   */
  executeScriptRequest(socket_id, response, handler) {
    logger.debug(`Executing script request`, { TAG, socket_id });

    this.setSessionData(socket_id, kCurrentHealingStatus, HEALING_STATUS.EXECUTE_SCRIPT);
    const waitForRequestSuccess = this.isPlaywrightRequestSuccess(response);
    logger.debug(`WaitFor request success: ${waitForRequestSuccess} :: ${response}`, { TAG, socket_id });
    this.setSessionData(socket_id, kWaitForRequestSuccess, waitForRequestSuccess);

    const originalRequest = this.getSessionData(socket_id, kOriginalRequest);
    const originalLocator = originalRequest?.params?.selector;

    if (!originalRequest?.guid || !originalRequest?.metadata) {
      logger.warn(`Missing required fields in original request`, { TAG, socket_id });
      return;
    }

    const script = this.sendToTcgScript(socket_id, waitForRequestSuccess, originalLocator);
    const requestData = {
      id: this.getNewHealingRequestId(socket_id),
      guid: originalRequest.guid,
      method: "evaluateExpression",
      params: {
        expression: `() => { ${script} }`,
        isFunction: true,
        arg: {
          value: { v: "undefined" },
          handles: []
        }
      },
      metadata: {
        apiName: 'page.evaluate',
        location: originalRequest.metadata?.location,
        internal: false
      }
    };

    logger.debug(`Sending script evaluation request to terminal`, { TAG, socket_id });
    handler.sendToTerminal(requestData, socket_id);
  }

  sendToTcgScript(socketId, waitForRequestSuccess, originalLocator) {
    const sessionId = this.getSessionData(socketId, RAILS_SESSION_ID)
    const sessionKeyObj = this.getRegistryObject(sessionId);
    const tcgDetails = sessionKeyObj.aiTcgDetails.replace(/'/g, '\\\'').replace(/"/g, '\\"');
    const sessionDetails = sessionKeyObj.aiSessionDetails || {};
    const testName = sessionDetails.session_name;
    const projectName = sessionDetails.project_name;
    const groupAIEnabled = sessionDetails.tcg_ai_enabled; // REMOVE
    const groupId = sessionKeyObj.group_id;
    const findElementTimeout = isTrueString(sessionDetails.implicit_delay_enabled) ? 0 : sessionDetails.find_element_timeout;

    return waitForRequestSuccess
      ? `
        window.dispatchEvent(new CustomEvent('ai-heal-find-element-success', {
          detail: {
            locatorType: 'playwright_locator',
            playwright_locator: '${originalLocator}',
            testName: '${testName}',
            projectName: '${projectName}',
            groupId: '${groupId}',
            sessionId: '${sessionId}',
            tcgDetails: '${tcgDetails}',
            isTestBedDataCollectionEnabled: ${sessionDetails.is_test_bed_data_collection_enabled}
          }
        }))
      `
      : `
        window.dispatchEvent(new CustomEvent('ai-heal-find-element-failure', {
          detail: {
            locatorType: 'playwright_locator',
            playwright_locator: '${originalLocator}',
            testName: '${testName}',
            projectName: '${projectName}',
            groupId: '${groupId}',
            sessionId: '${sessionId}',
            tcgDetails: '${tcgDetails}',
            groupAIEnabled: '${groupAIEnabled}',
            findElementTimeout: '${findElementTimeout}',
            grrRegion: '${sessionDetails.grr_region}',
            isTestBedDataCollectionEnabled: ${sessionDetails.is_test_bed_data_collection_enabled}
          }
        }))
      `;
  }

  /**
   * Gets healed selector from TCG and makes the request
   * @param {string} socket_id - Session ID
   * @param {Object} handler - Handler to send requests to terminal
   */
  async healedSelectorRequest(socket_id, response, handler) {
    logger.debug(`Processing healed selector request`, { TAG, socket_id });
    this.setSessionData(socket_id, kCurrentHealingStatus, HEALING_STATUS.HEALED_SELECTOR);
    const waitForRequestSuccess = this.getSessionData(socket_id, kWaitForRequestSuccess);
    if (waitForRequestSuccess) {
      logger.debug(`WaitFor request succeeded, using original selector`, { TAG, socket_id });
      this.originalSelectorRequest(socket_id, handler);
      return;
    }

    try {
      const healedSelector = await this.getHealedSelector(socket_id);

      if (!healedSelector) {
        logger.warn(`Failed to get healed selector, falling back to original`, { TAG, socket_id });
        this.originalSelectorRequest(socket_id, handler);
        return;
      }

      const aiType = this.aiSessions.get(socket_id);
      this.setSessionData(socket_id, kHealedSelector, healedSelector);
      if (aiType === kSoftHeal) {
        logger.debug(`Soft healing applied with selector: ${healedSelector}`, { TAG, socket_id });
        this.recordHealingSuccess(socket_id);
        this.originalSelectorRequest(socket_id, handler);
        return;
      }

      logger.debug(`Got healed selector: ${healedSelector}`, { TAG, socket_id });
      const healingRequestData = this.getSessionData(socket_id, kOriginalRequest);

      if (!healingRequestData?.params) {
        logger.warn(`Missing params in original request`, { TAG, socket_id });
        this.originalSelectorRequest(socket_id, handler);
        return;
      }

      // Update selector with healed version
      healingRequestData.params.selector = healedSelector;

      logger.debug(`Sending request with healed selector`, { TAG, socket_id });
      handler.sendToTerminal(healingRequestData, socket_id);
    } catch (error) {
      logger.error(`Error getting healed selector: ${error.message}`, { TAG, socket_id });
      this.originalSelectorRequest(socket_id, handler);
    }
  }

  /**
   * Records healing success in logs and metrics
   * @param {string} socket_id - Session ID
   */
  recordHealingSuccess(socket_id) {
    const sessionID = this.getSessionData(socket_id, RAILS_SESSION_ID);
    const keyObject = this.getRegistryObject(sessionID);
    let healedSelector;
    // Sorry for this T_T, TODO: Handle healed selector logging in a better way
    const aiType = this.aiSessions.get(socket_id);
    try {
      const keyObjectDiffHash = {};

      if (aiType === kSoftHeal) {
        if (!isTrueString(keyObject.softHealingSuccess)) {
          keyObject.softHealingSuccess = 'true';
          keyObjectDiffHash.softHealingSuccess = 'true';
        }
        healedSelector = this.getSessionData(socket_id, kHealedSelector)?.split("=");
      } else {
        if (!isTrueString(keyObject.selfHealingSuccess)) {
          keyObject.selfHealingSuccess = 'true';
          keyObjectDiffHash.selfHealingSuccess = 'true';
        }
        healedSelector = this.getSessionData(socket_id, kOriginalRequest)?.params?.selector?.split("=");
      }

      if (healedSelector) {
        keyObject.healedSelector = {
          using: healedSelector.shift(),
          value: healedSelector.join("="),
          selfHeal: aiType === kSelfHeal ? true : false
        };
        keyObjectDiffHash.healedSelector = keyObject.healedSelector;
      }

      // Use pubSub to update other processes with the changes
      if (Object.keys(keyObjectDiffHash).length > 0) {
        logger.debug(`Publishing healing success status update`, { TAG, socket_id, keyObjectDiffHash });
        pubSub.publish(constants.updateKeyObject, {
          session: sessionID,
          changed: keyObjectDiffHash
        });
      }

      // Record healing end time and calculate total healing time
      const healingStartTime = this.getSessionData(socket_id, kHealingStartTime);
      const healingEndTime = Date.now();
      this.setSessionData(socket_id, kHealingEndTime, healingEndTime);

      // Calculate total healing time in ms
      const totalHealingTime = healingEndTime - healingStartTime;

      // Log the healing time
      logger.debug(`Total healing time: ${totalHealingTime}ms`, { TAG, socket_id });

      // Store healing time in keyObject for later reference
      if (!keyObject.playwrightAiMetrics) {
        keyObject.playwrightAiMetrics = {};
      }
      keyObject.playwrightAiMetrics.healingTime = totalHealingTime;
      keyObject.playwrightAiMetrics.cumulativeHealingTime =
        (keyObject.playwrightAiMetrics.cumulativeHealingTime || 0) + totalHealingTime;

      // Also store in ai_healing_details directly for compatibility with helper.js
      if (!keyObject.ai_healing_details) {
        keyObject.ai_healing_details = {};
      }
      keyObject.ai_healing_details.total_healing_duration = keyObject.playwrightAiMetrics.cumulativeHealingTime;
    } catch (error) {
      logger.debug(`Error while getting healed selector: ${error.message}`, { TAG, socket_id });
    }
  }

  /**
   * Makes the original request sent from the client
   * @param {string} socket_id - Session ID
   * @param {Object} handler - Handler to send requests to terminal
   */
  originalSelectorRequest(socket_id, handler) {
    logger.debug(`Making original selector request`, { TAG, socket_id });

    this.setSessionData(socket_id, kCurrentHealingStatus, HEALING_STATUS.ORIGINAL_SELECTOR);

    const originalRequestData = this.getSessionData(socket_id, kOriginalRequest);

    if (!originalRequestData) {
      logger.warn(`Missing original request data`, { TAG, socket_id });
      return;
    }

    // Check if we have a start time (healing was attempted but failed)
    const healingStartTime = this.getSessionData(socket_id, kHealingStartTime);
    if (healingStartTime) {
      const healingEndTime = Date.now();
      this.setSessionData(socket_id, kHealingEndTime, healingEndTime);

      // Calculate total healing time even though it failed
      const totalHealingTime = healingEndTime - healingStartTime;

      // Log the time spent on attempted healing
      logger.debug(`Failed healing attempt time: ${totalHealingTime}ms`, { TAG, socket_id });

      // Store metrics for failed healing attempt
      const sessionID = this.getSessionData(socket_id, RAILS_SESSION_ID);
      const keyObject = this.getRegistryObject(sessionID);

      if (!keyObject.playwrightAiMetrics) {
        keyObject.playwrightAiMetrics = {};
      }
      keyObject.playwrightAiMetrics.healingTime = totalHealingTime;
      keyObject.playwrightAiMetrics.cumulativeHealingTime =
        (keyObject.playwrightAiMetrics.cumulativeHealingTime || 0) + totalHealingTime;

      // Also store in ai_healing_details directly for compatibility with helper.js
      if (!keyObject.ai_healing_details) {
        keyObject.ai_healing_details = {};
      }
      keyObject.ai_healing_details.total_healing_duration = keyObject.playwrightAiMetrics.cumulativeHealingTime;
    }

    handler.sendToTerminal(originalRequestData, socket_id);
  }
  /**
   * Processes healing response based on current healing status
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response from terminal
   * @param {Object} handler - Handler to send requests to terminal
   * @returns {boolean} - Whether healing process is complete
   */
  processHealingResponse(socket_id, response, handler) {
    logger.debug(`Processing healing response`, { TAG, socket_id });

    if (!handler) {
      logger.warn(`Handler not provided, skipping healing`, { TAG, socket_id });
      return true;
    }

    const healingStatus = this.getSessionData(socket_id, kCurrentHealingStatus);
    if (healingStatus === HEALING_STATUS.WAIT) {
      logger.debug(`Handling waitFor response`, { TAG, socket_id });
      this.handleWaitForResponse(socket_id, response, handler);
      return false;
    } else if (healingStatus === HEALING_STATUS.EXECUTE_SCRIPT) {
      logger.debug(`Handling executeScript response`, { TAG, socket_id });
      this.handleExecuteScriptResponse(socket_id, response, handler);
      return false;
    } else if (healingStatus === HEALING_STATUS.HEALED_SELECTOR) {
      logger.debug(`Handling healed selector response`, { TAG, socket_id });
      return this.handleHealedSelectorResponse(socket_id, response, handler);
    } else if (healingStatus === HEALING_STATUS.ORIGINAL_SELECTOR) {
      logger.debug(`Handling original selector response`, { TAG, socket_id });
      this.handleOriginalSelectorResponse(socket_id, response, handler);
      return true;
    }

    logger.warn(`Unrecognized healing status: ${healingStatus}`, { TAG, socket_id });
    return true;
  }

  /**
   * Handles waitFor response by initiating executeScript request
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response from waitFor request
   * @param {Object} handler - Handler to send requests to terminal
   */
  handleWaitForResponse(socket_id, response, handler) {
    this.executeScriptRequest(socket_id, response, handler);
  }

  /**
   * Handles executeScript response by initiating healedSelector request
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response from executeScript request
   * @param {Object} handler - Handler to send requests to terminal
   */
  handleExecuteScriptResponse(socket_id, response, handler) {
    logger.debug(`Handling executeScript response`, { TAG, socket_id });
    this.healedSelectorRequest(socket_id, response, handler);
  }

  /**
   * Handles healed selector response, falling back to original if failed
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response from healedSelector request
   * @param {Object} handler - Handler to send requests to terminal
   * @returns {boolean} - Whether healing succeeded
   */
  handleHealedSelectorResponse(socket_id, response, handler) {
    logger.debug(`Handling healed selector response`, { TAG, socket_id });

    const healedSelectorRequestSuccess = this.isPlaywrightRequestSuccess(response);

    if (!healedSelectorRequestSuccess) {
      logger.warn(`Healed selector request failed, falling back to original`, { TAG, socket_id });
      this.originalSelectorRequest(socket_id, handler);
      return false;
    }

    this.recordHealingSuccess(socket_id);
    logger.debug(`Healed selector request succeeded`, { TAG, socket_id });
    return true;
  }

  /**
   * Handles original selector response by cleaning up session data
   * @param {string} socket_id - Session ID
   * @param {Object} response - Response object
   */
  handleOriginalSelectorResponse(socket_id, response) {
    logger.debug(`Handling original selector response`, { TAG, socket_id });

    // Clear healing data
    this.setSessionData(socket_id, kOriginalRequest, null);
    this.setSessionData(socket_id, kCurrentHealingStatus, null);
    this.setSessionData(socket_id, kWaitForRequestSuccess, null);
  }
  /**
   * Gets healed selector from TCG
   * @param {string} socket_id - Session ID
   * @returns {Promise<string|null>} - Healed selector or null if not available
   */
  async getHealedSelector(socket_id) {
    logger.debug(`Getting healed selector from TCG`, { TAG, socket_id });
    try {
      const { success, data: tcgResponse } = await this.makeRequestTcg(socket_id);
      if (success.toString() === 'true' && isNotUndefined(tcgResponse)) {
        let using;
        let value;
        using = Object.keys(tcgResponse).shift();
        value = tcgResponse[using];
        if (isUndefined(using) || isUndefined(value)) {
          return null;
        }
        using = PLAYWRIGHT_SELECTOR_MAP[using];
        return `${using}=${value} >> nth=0`;
      }
    } catch (error) {
      logger.debug(`Error while getting healing selector : ${error}`, { TAG, socket_id });
    }
    return null;
  }

  // TODO: Follow up PR, move this method and the getResultsFromTcg method from AICommandHandler to AICommandHelper
  async makeRequestTcg(socket_id) {
    logger.debug(`Getting healed selector from TCG`, { TAG, socket_id });
    let retries = TCG_ENDPOINTS.GET_RESULT.retries;
    while (retries >= 1) {
      try {
        const sessionId = this.getSessionData(socket_id, RAILS_SESSION_ID)
        const data = `{"data":{"sessionId":"${sessionId}"}}`;
        const keyObject = this.getRegistryObject(sessionId);
        let response = { data: '{}' };
        response = await AICommandHelper.makeRequestTcg(TCG_ENDPOINTS.GET_RESULT.method,
          TCG_ENDPOINTS.GET_RESULT.path, data, TCG_HEADERS, keyObject.aiTcgHostname);
        const tcgData = JSON.parse(response.data);

        logger.debug(`Response from TCG: ${response.data}`, { TAG, socket_id });
        if (response.statusCode !== 200 || isUndefined(tcgData) || (tcgData.success || false).toString() !== 'true') {
          logger.warn(`Failed to get healed selector from TCG`, {
            TAG,
            socket_id,
            success: response.success,
            error: response.error
          });
          await sleep(TCG_ENDPOINTS.GET_RESULT.sleep);
        } else {
          return tcgData;
        }
      } catch (error) {
        logger.error(`Error calling TCG: ${error.message}`, { TAG, socket_id });
      }
      retries -= 1;
    }
    return { success: false, data: null };
  }

}

module.exports = SocketAiHandler;
