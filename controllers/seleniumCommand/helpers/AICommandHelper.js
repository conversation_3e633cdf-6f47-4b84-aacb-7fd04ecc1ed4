'use strict';

const helper = require('../../../helper');
const constants = require('../../../constants');
const requestlib = require('../../../lib/request');
const {
  isNotUndefined, isString
} = require('../../../typeSanity');

const AI_REDIS_KEY = 'AI_COMMAND_LIST_';
exports.AI_REDIS_KEY = AI_REDIS_KEY;
exports.PUBLISH_AI_SUCCESS = 'publish_ai';
exports.PUBLISH_AI_MID_SESSION_FAILURE = 'publish_ai_mid_session_failure';

exports.getRedisKey = railsSessionId => `${AI_REDIS_KEY}${railsSessionId}`;

const calculateTimeDiff = initTime => (Date.now() - initTime);

exports.constructCommand = (method, reqUrl, initTime) =>
  /* The equivalent of the following JSON without any spaces to optimise redis storage
  {
    key: `${method}:${reqUrl}`,
    time_diff: calculateTimeDiff(initTime)
  }
  */
  `{"k":"${method}:${reqUrl}","t":"${calculateTimeDiff(initTime)}"},`;


exports.getRedisClient = () => helper.redisClientAi;

exports.TCG_ENDPOINTS = {
  AUTH: {
    method: 'POST',
    path: '/auth/set-token',
  },
  GET_RESULT: {
    method: 'POST',
    path: '/healing/get-result',
    retries: 20,
    sleep: 1 * 1000, // 1 seconds
  },
};
exports.LOCAL_STORAGE_RETRIES = 1;
exports.TCG_HEADERS = {
  'content-type': 'application/json',
  'x-bstack-client-version': '1.1.0'
};
exports.makeRequestTcg = (method, path, body, headers, hostname) => {
  const options = {
    method,
    path,
    body,
    headers,
    hostname,
    scheme: constants.TCG_SERVICE.scheme,
    auth: `${constants.TCG_SERVICE.username}:${constants.TCG_SERVICE.password}`,
    port: '',
  };
  return requestlib.call(options);
};

exports.getTcgEndpoint = () => constants.TCG_SERVICE.regions[constants.region].endpoint;
exports.getTcgS3UploadEndpoint = () => constants.TCG_SERVICE.regions[constants.region].s3_upload_endpoint;
exports.getLcncSessionGroups = () => constants.LCNC_GROUPS;

const DEFAULT_TCG_ENDPOINT = 'tcg.browserstack.com';
const DEFAULT_TCG_S3_ENDPOINT = 'browserstack-testcasegeneration-prod-use1.s3.us-east-1.amazonaws.com';
const DEFAULT_EDS_ENDPOINT = 'eds.browserstack.com';
const DEFAULT_LCNC_SESSION_GROUPS = [7079242];

exports.DEFAULT_TCG_ENDPOINT = DEFAULT_TCG_ENDPOINT;
exports.DEFAULT_TCG_S3_ENDPOINT = DEFAULT_TCG_S3_ENDPOINT;
exports.DEFAULT_EDS_ENDPOINT = DEFAULT_EDS_ENDPOINT;
exports.getFirecmdTcgOptions = (options) => {
  const lcncGroups = this.getLcncSessionGroups() || DEFAULT_LCNC_SESSION_GROUPS;
  if ((options.browserstackParams['browserstack.ai_enabled_session'] &&
      options.browserstackParams['browserstack.ai_enabled_session'].toString() === 'true' &&
      !options.realMobile) ||
      lcncGroups.includes(options.group_id)) {
    return JSON.stringify({
      enabled: true,
      tcg_scheme: constants.TCG_SERVICE.scheme,
      tcg_hostname: this.getTcgEndpoint() || DEFAULT_TCG_ENDPOINT,
      tcg_s3_upload_hostname: this.getTcgS3UploadEndpoint() || DEFAULT_TCG_S3_ENDPOINT,
      eds_hostname: DEFAULT_EDS_ENDPOINT
    });
  }
  return '{ "enabled": "false" }';
};

function getAIControlFlags(options) {
  const aiDetails = typeof options.browserstackParams['browserstack.ai_details'] === 'string'
    ? JSON.parse(options.browserstackParams['browserstack.ai_details'] || '{}')
    : options.browserstackParams['browserstack.ai_details'];
  const _isSelfHeal = aiDetails && aiDetails.enable_ai_healing && aiDetails.enable_ai_healing.toString() === 'true';
  const _isSoftHeal = aiDetails && aiDetails.ai_soft_heal && aiDetails.ai_soft_heal.toString() === 'true';
  const _isTestbedEnabled = aiDetails && aiDetails.is_test_bed_data_collection_enabled && aiDetails.is_test_bed_data_collection_enabled.toString() === 'true';

  return [_isSelfHeal, _isSoftHeal, _isTestbedEnabled];
}

exports.getFirecmdAIProxyOptions = (options) => {
  const [isSelfHeal, isSoftHeal, isTestbedEnabled] = getAIControlFlags(options);
  let enableHealing = isSelfHeal; // Using this variable for spawning AIProxy

  const aiProxyKeyObject = {
    sessionID: options.sessionId || null,
    seleniumVersion: options.browserstackParams['browserstack.selenium.jar.version'] || null,
    dialect: options.dialect,
    groupId: options.group_id,
    userId: options.user_id
  };

  if (isNotUndefined(options.bsCaps) && isNotUndefined(options.bsCaps.app)) {
    aiProxyKeyObject.bundleId = options.bsCaps.bundleID;
    aiProxyKeyObject.os = options.bsCaps.orig_os;
    aiProxyKeyObject.healingEnabled = isSelfHeal || isSoftHeal;
    aiProxyKeyObject.testbedCollection = isTestbedEnabled;
    enableHealing = isSelfHeal || isSoftHeal || isTestbedEnabled;
  }

  if (options.browserstackParams['browserstack.ai_enabled_session'] && options.browserstackParams['browserstack.ai_enabled_session'].toString() === 'true' && options.realMobile && enableHealing) {
    return JSON.stringify({
      enabled: true,
      keyObject: aiProxyKeyObject,
      tcgService: constants.TCG_SERVICE,
      tcgUrl: `https://${this.getTcgEndpoint() || DEFAULT_TCG_ENDPOINT}/`,
    });
  }
  return '{ "enabled": "false" }';
};

exports.getSanitisedString = data => (isNotUndefined(data) && isString(data) ? data.replace(/\\'/g, '\'').replace(/\\"/g, '"').replace(/'/g, '\\\'').replace(/"/g, '\\"') : '');

exports.evaluateKeyObjectDiff = (keyObject, keyObjectDiffHash) => {
  if (keyObject.selfHealingSuccess === this.PUBLISH_AI_SUCCESS) {
    keyObject.selfHealingSuccess = 'true';
    keyObjectDiffHash.selfHealingSuccess = keyObject.selfHealingSuccess;
  } else if (keyObject.softHealingSuccess === this.PUBLISH_AI_SUCCESS) {
    keyObject.softHealingSuccess = 'true';
    keyObjectDiffHash.softHealingSuccess = keyObject.softHealingSuccess;
  }
  if (keyObject.midSessionHealingDisabled === this.PUBLISH_AI_MID_SESSION_FAILURE) {
    keyObject.midSessionHealingDisabled = 'true';
  }
};

exports.updateAIHealingDetails = (sessionKeyObj, key) => {
  if (sessionKeyObj && sessionKeyObj.ai_healing_details && key in sessionKeyObj.ai_healing_details) {
    sessionKeyObj.ai_healing_details[key] += 1;
  }
};

/**
 * Updates the total_healing_duration field in ai_healing_details with a specific duration value
 * @param {Object} sessionKeyObj - The session key object
 * @param {number} duration - The healing duration to add (in milliseconds)
 */
exports.updateAIHealingDuration = (sessionKeyObj, duration) => {
  if (sessionKeyObj &&
      sessionKeyObj.ai_healing_details &&
      'total_healing_duration' in sessionKeyObj.ai_healing_details &&
      typeof duration === 'number') {
    sessionKeyObj.ai_healing_details.total_healing_duration += duration;
  }
};

exports.PLAYWRIGHT_SELECTOR_MAP = {
  'css selector': 'css',
  xpath: 'xpath',
  text: 'text',
  'text selector': 'text',
  role: 'role',
  id: 'id',
  name: 'name',
  placeholder: 'placeholder',
  'alt text': 'altText',
  label: 'label',
  'test id': 'testId',
  testid: 'testId',
};
