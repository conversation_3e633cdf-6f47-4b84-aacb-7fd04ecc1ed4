'use strict';

const { expect } = require('chai');
const sinon = require('sinon');
const rewire = require('rewire');

// Import the module using rewire for testing private methods
const SocketAiHandlerModule = rewire('../../../helpers/aiHelper/playwrightAiHelper');
const SocketAiHandler = SocketAiHandlerModule;

// Mocks
const loggerMock = {
  debug: sinon.stub(),
  info: sinon.stub(),
  warn: sinon.stub(),
  error: sinon.stub()
};

const globalRegistryMock = {};

// Get the actual symbols from the module
const kOriginalRequest = SocketAiHandlerModule.__get__('kOriginalRequest');
const kCurrentHealingStatus = SocketAiHandlerModule.__get__('kCurrentHealingStatus');
const kSelfHeal = SocketAiHandlerModule.__get__('kSelfHeal');
const kSoftHeal = SocketAiHandlerModule.__get__('kSoftHeal');
const kHealedSelector = SocketAiHandlerModule.__get__('kHealedSelector');
const kWaitForRequestSuccess = SocketAiHandlerModule.__get__('kWaitForRequestSuccess');
const kHealingRequestId = SocketAiHandlerModule.__get__('kHealingRequestId');
const HEALING_STATUS = SocketAiHandlerModule.__get__('HEALING_STATUS');
const WAIT_FOR_METHOD = SocketAiHandlerModule.__get__('WAIT_FOR_METHOD');
const RAILS_SESSION_ID = SocketAiHandlerModule.__get__('RAILS_SESSION_ID');
const BASE_HEALING_REQUEST_ID_VALUE = SocketAiHandlerModule.__get__('BASE_HEALING_REQUEST_ID_VALUE');
const TCG_ENDPOINTS = SocketAiHandlerModule.__get__('TCG_ENDPOINTS');
const TCG_HEADERS = SocketAiHandlerModule.__get__('TCG_HEADERS');

// Mock AICommandHelper
const AICommandHelperMock = {
  makeRequestTcg: sinon.stub()
};

describe('SocketAiHandler', () => {
  let socketAiHandler;
  let handlerMock;

  beforeEach(() => {
    // Reset stubs
    loggerMock.debug.reset();
    loggerMock.info.reset();
    loggerMock.warn.reset();
    loggerMock.error.reset();

    // Set up test dependencies and mocks
    SocketAiHandlerModule.__set__({
      logger: loggerMock,
      'constants.global_registry': globalRegistryMock,
    });

    // Create a new instance for each test
    socketAiHandler = new SocketAiHandler();

    // Mock handler for terminal communication
    handlerMock = {
      sendToTerminal: sinon.stub()
    };

    // Reset global registry mock
    Object.keys(globalRegistryMock).forEach(key => {
      delete globalRegistryMock[key];
    });
  });

  describe('constructor', () => {
    it('should initialize session data maps', () => {
      expect(socketAiHandler.sessionData).to.be.instanceof(Map);
      expect(socketAiHandler.sessionData.size).to.equal(0);
      expect(socketAiHandler.aiSessions).to.be.instanceof(Map);
      expect(socketAiHandler.aiSessions.size).to.equal(0);
    });
  });

  describe('#getSessionData', () => {
    it('should return undefined for non-existent session', () => {
      const result = socketAiHandler.getSessionData('test-socket-id', 'some-key');
      expect(result).to.be.undefined;
    });

    it('should return undefined for non-existent key in valid session', () => {
      socketAiHandler.sessionData.set('test-socket-id', new Map());
      const result = socketAiHandler.getSessionData('test-socket-id', 'some-key');
      expect(result).to.be.undefined;
    });

    it('should return the value for an existing key', () => {
      const sessionMap = new Map();
      sessionMap.set('some-key', 'some-value');
      socketAiHandler.sessionData.set('test-socket-id', sessionMap);

      const result = socketAiHandler.getSessionData('test-socket-id', 'some-key');
      expect(result).to.equal('some-value');
    });

    it('should handle Symbol keys correctly', () => {
      const testSymbol = Symbol('test-symbol');
      const sessionMap = new Map();
      sessionMap.set(testSymbol, 'symbol-value');
      socketAiHandler.sessionData.set('test-socket-id', sessionMap);

      const result = socketAiHandler.getSessionData('test-socket-id', testSymbol);
      expect(result).to.equal('symbol-value');
    });
  });

  describe('#setSessionData', () => {
    it('should create a new session map if one does not exist', () => {
      socketAiHandler.setSessionData('test-socket-id', 'some-key', 'some-value');

      expect(socketAiHandler.sessionData.has('test-socket-id')).to.be.true;
      const sessionMap = socketAiHandler.sessionData.get('test-socket-id');
      expect(sessionMap).to.be.instanceof(Map);
      expect(sessionMap.get('some-key')).to.equal('some-value');
    });

    it('should update an existing value', () => {
      const sessionMap = new Map();
      sessionMap.set('some-key', 'old-value');
      socketAiHandler.sessionData.set('test-socket-id', sessionMap);

      socketAiHandler.setSessionData('test-socket-id', 'some-key', 'new-value');

      expect(sessionMap.get('some-key')).to.equal('new-value');
    });

    it('should handle Symbol keys correctly', () => {
      const testSymbol = Symbol('test-symbol');
      socketAiHandler.setSessionData('test-socket-id', testSymbol, 'symbol-value');

      const sessionMap = socketAiHandler.sessionData.get('test-socket-id');
      expect(sessionMap.get(testSymbol)).to.equal('symbol-value');
    });
  });

  describe('#clearSessionState', () => {
    it('should remove session data and AI session entry', () => {
      // Set up test data
      socketAiHandler.sessionData.set('test-socket-id', new Map());
      socketAiHandler.aiSessions.set('test-socket-id', Symbol('selfHeal'));

      socketAiHandler.clearSessionState('test-socket-id');

      expect(socketAiHandler.sessionData.has('test-socket-id')).to.be.false;
      expect(socketAiHandler.aiSessions.has('test-socket-id')).to.be.false;
      expect(loggerMock.debug.calledOnce).to.be.true;
    });

    it('should handle non-existent socket IDs gracefully', () => {
      socketAiHandler.clearSessionState('non-existent-id');
      expect(loggerMock.debug.calledOnce).to.be.true;
    });
  });

  describe('#isRequestHealable', () => {
    it('should return true for healable methods with valid selectors', () => {
      const healableMethods = ['fill', 'selectOption', 'click'];

      healableMethods.forEach(method => {
        const request = {
          method,
          params: { selector: 'valid-selector' }
        };

        expect(socketAiHandler.isRequestHealable(request)).to.be.true;
      });
    });

    it('should return false for non-healable methods', () => {
      const request = {
        method: 'nonHealableMethod',
        params: { selector: 'valid-selector' }
      };

      expect(socketAiHandler.isRequestHealable(request)).to.be.false;
    });

    it('should return false for selectors containing >>', () => {
      const request = {
        method: 'fill',
        params: { selector: 'element >> visible=true' }
      };

      expect(socketAiHandler.isRequestHealable(request)).to.be.false;
    });

    it('should handle undefined or null values gracefully', () => {
      expect(socketAiHandler.isRequestHealable(null)).to.be.false;
      expect(socketAiHandler.isRequestHealable({})).to.be.false;
      expect(socketAiHandler.isRequestHealable({ method: 'abc' })).to.be.false;
    });
  });

  describe('#isPlaywrightRequestSuccess', () => {
    it('should return true when response has no error and log', () => {
      const response = { id: 1, result: {} };
      expect(socketAiHandler.isPlaywrightRequestSuccess(response)).to.be.true;
    });

    it('should return false when response has error and log', () => {
      const response = { id: 1, error: 'error message', log: 'log message' };
      expect(socketAiHandler.isPlaywrightRequestSuccess(response)).to.be.false;
    });

    it('should return true when only one of error or log is present', () => {
      const response1 = { id: 1, error: 'error message' };
      const response2 = { id: 1, log: 'log message' };

      expect(socketAiHandler.isPlaywrightRequestSuccess(response1)).to.be.true;
      expect(socketAiHandler.isPlaywrightRequestSuccess(response2)).to.be.true;
    });
  });

  describe('#waitForRequest', () => {
    // Get the actual symbols from the module
    const kOriginalRequest = SocketAiHandlerModule.__get__('kOriginalRequest');
    const kCurrentHealingStatus = SocketAiHandlerModule.__get__('kCurrentHealingStatus');
    const HEALING_STATUS = SocketAiHandlerModule.__get__('HEALING_STATUS');
    const WAIT_FOR_METHOD = SocketAiHandlerModule.__get__('WAIT_FOR_METHOD');

    beforeEach(() => {
      sinon.stub(socketAiHandler, 'isRequestHealable');
      sinon.stub(socketAiHandler, 'getNewHealingRequestId');
    });

    afterEach(() => {
      socketAiHandler.isRequestHealable.restore();
      socketAiHandler.getNewHealingRequestId.restore();
    });

    it('should return original request if not healable', () => {
      socketAiHandler.isRequestHealable.returns(false);

      const request = { id: 1, method: 'someMethod' };
      const result = socketAiHandler.waitForRequest('test-socket-id', request);

      expect(result).to.equal(request);
    });

    it('should create a waitFor request for healable requests', () => {
      // Set up stubs
      socketAiHandler.isRequestHealable.returns(true);
      socketAiHandler.getNewHealingRequestId.returns(100001);
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);

      // Spy on setSessionData to verify it's called correctly
      const setSessionDataSpy = sinon.spy(socketAiHandler, 'setSessionData');

      const request = {
        id: 1,
        method: 'fill',
        params: { selector: 'input#username' },
        guid: 'some-guid',
        metadata: { someMetadata: true }
      };

      const result = socketAiHandler.waitForRequest('test-socket-id', request);

      // Instead of object identity comparison, check specific properties
      expect(result.id).to.equal(100001);
      expect(result.method).to.equal('waitForSelector');
      expect(result.params).to.deep.equal(request.params);
      expect(result.guid).to.equal(request.guid);
      expect(result.metadata).to.deep.equal(request.metadata);

      // Verify that setSessionData was called with the correct parameters
      expect(setSessionDataSpy.calledWith('test-socket-id', kOriginalRequest, sinon.match.object)).to.be.true;
      expect(setSessionDataSpy.calledWith('test-socket-id', kCurrentHealingStatus, HEALING_STATUS.WAIT)).to.be.true;

      // Get the first call to setSessionData and verify the stored request
      const storedRequest = setSessionDataSpy.args.find(args => String(args[1]) === String(kOriginalRequest))[2];
      expect(storedRequest).to.deep.equal(request);

      // Clean up the spy
      setSessionDataSpy.restore();
    });
  });

  describe('#processHealingResponse', () => {
    const kCurrentHealingStatus = Symbol.for('healingStatus');
    const HEALING_STATUS = SocketAiHandlerModule.__get__('HEALING_STATUS');

    beforeEach(() => {
      sinon.stub(socketAiHandler, 'handleWaitForResponse');
      sinon.stub(socketAiHandler, 'handleExecuteScriptResponse');
      sinon.stub(socketAiHandler, 'handleHealedSelectorResponse');
      sinon.stub(socketAiHandler, 'handleOriginalSelectorResponse');
      sinon.stub(socketAiHandler, 'getSessionData');
    });

    afterEach(() => {
      socketAiHandler.handleWaitForResponse.restore();
      socketAiHandler.handleExecuteScriptResponse.restore();
      socketAiHandler.handleHealedSelectorResponse.restore();
      socketAiHandler.handleOriginalSelectorResponse.restore();
      socketAiHandler.getSessionData.restore();
    });

    it('should return true if handler is not provided', () => {
      const result = socketAiHandler.processHealingResponse('test-socket-id', {});
      expect(result).to.be.true;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });

    it('should call handleWaitForResponse for WAIT status', () => {
      socketAiHandler.getSessionData.returns(HEALING_STATUS.WAIT);

      const response = { id: 1 };
      const result = socketAiHandler.processHealingResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.false;
      expect(socketAiHandler.handleWaitForResponse.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });

    it('should call handleExecuteScriptResponse for EXECUTE_SCRIPT status', () => {
      socketAiHandler.getSessionData.returns(HEALING_STATUS.EXECUTE_SCRIPT);

      const response = { id: 1 };
      const result = socketAiHandler.processHealingResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.false;
      expect(socketAiHandler.handleExecuteScriptResponse.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });

    it('should call handleHealedSelectorResponse for HEALED_SELECTOR status', () => {
      socketAiHandler.getSessionData.returns(HEALING_STATUS.HEALED_SELECTOR);
      socketAiHandler.handleHealedSelectorResponse.returns(true);

      const response = { id: 1 };
      const result = socketAiHandler.processHealingResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.true;
      expect(socketAiHandler.handleHealedSelectorResponse.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });

    it('should call handleOriginalSelectorResponse for ORIGINAL_SELECTOR status', () => {
      socketAiHandler.getSessionData.returns(HEALING_STATUS.ORIGINAL_SELECTOR);

      const response = { id: 1 };
      const result = socketAiHandler.processHealingResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.true;
      expect(socketAiHandler.handleOriginalSelectorResponse.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });

    it('should return true for unknown healing status', () => {
      socketAiHandler.getSessionData.returns('unknown-status');

      const response = { id: 1 };
      const result = socketAiHandler.processHealingResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.true;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });
  });

  describe('#sendToTcgScript', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'getRegistryObject');
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.getRegistryObject.restore();
    });

    it('should generate success script when waitForRequestSuccess is true', () => {
      // Setup mocks
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({
        aiSessionDetails: {
          session_name: 'test-name',
          project_name: 'test-project',
          playwrightLocator: '#test-selector',
          rootId: 'root-123',
          referenceId: 'ref-123',
          isGetShadowRoot: false,
          is_test_bed_data_collection_enabled: true
        },
        group_id: 'group-123',
        aiTcgDetails: '{}'
      });

      const result = socketAiHandler.sendToTcgScript('test-socket-id', true, '#test-selector');
      expect(result).to.include('window.dispatchEvent(new CustomEvent(\'ai-heal-find-element-success\'');
      expect(result).to.include('playwright_locator: \'#test-selector\'');
      expect(result).to.include('testName: \'test-name\'');
      expect(result).to.include('projectName: \'test-project\'');
      expect(result).to.include('groupId: \'group-123\'');
      expect(result).to.include('sessionId: \'test-session-id\'');
      expect(result).to.include('tcgDetails: \'{}\'');
      expect(result).to.include('isTestBedDataCollectionEnabled: true');
    });

    it('should generate failure script when waitForRequestSuccess is false', () => {
      // Setup mocks
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({
        aiSessionDetails: {
          session_name: 'test-name',
          project_name: 'test-project',
          playwrightLocator: '#test-selector',
          tcg_ai_enabled: 'true',
          implicit_delay_enabled: 'false',
          find_element_timeout: '30',
          grr_region: 'us-east-1',
          is_test_bed_data_collection_enabled: true
        },
        group_id: 'group-123',
        aiTcgDetails: '{}'
      });

      const result = socketAiHandler.sendToTcgScript('test-socket-id', false, '#test-selector');

      expect(result).to.include('window.dispatchEvent(new CustomEvent(\'ai-heal-find-element-failure\'');
      expect(result).to.include('playwright_locator: \'#test-selector\'');
      expect(result).to.include('testName: \'test-name\'');
      expect(result).to.include('projectName: \'test-project\'');
      expect(result).to.include('groupId: \'group-123\'');
      expect(result).to.include('sessionId: \'test-session-id\'');
      expect(result).to.include('tcgDetails: \'{}\'');
      expect(result).to.include('groupAIEnabled: \'true\'');
      expect(result).to.include('findElementTimeout: \'30\'');
      expect(result).to.include('grrRegion: \'us-east-1\'');
    });

    it('should handle missing data gracefully', () => {
      // Setup mocks with minimal data
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({
        aiSessionDetails: {},
        group_id: undefined,
        aiTcgDetails: ''
      });

      const result = socketAiHandler.sendToTcgScript('test-socket-id', true, undefined);

      expect(result).to.include('playwright_locator: \'undefined\'');
      expect(result).to.include('testName: \'undefined\'');
      expect(result).to.include('projectName: \'undefined\'');
      expect(result).to.include('groupId: \'undefined\'');
      expect(result).to.include('tcgDetails: \'\'');
    });
  });

  describe('#getHealedSelector', () => {
    beforeEach(() => {
      // Stub the makeRequestTcg method of socketAiHandler
      sinon.stub(socketAiHandler, 'makeRequestTcg');

      // Also need to mock PLAYWRIGHT_SELECTOR_MAP
      SocketAiHandlerModule.__set__('PLAYWRIGHT_SELECTOR_MAP', {
        css: 'css'
      });
    });

    afterEach(() => {
      socketAiHandler.makeRequestTcg.restore();
    });

    it('should return selector when TCG request is successful', async () => {
      // Mock successful TCG response with Promise.resolve instead of .resolves
      socketAiHandler.makeRequestTcg.returns(Promise.resolve({
        success: true,
        data: { css: '#healed-selector' }
      }));

      const result = await socketAiHandler.getHealedSelector('test-socket-id');

      expect(result).to.equal('css=#healed-selector >> nth=0');
      expect(socketAiHandler.makeRequestTcg.calledOnce).to.be.true;
    });

    it('should return null when TCG request fails', async () => {
      // Mock failed TCG response with Promise.resolve instead of .resolves
      socketAiHandler.makeRequestTcg.returns(Promise.resolve({
        success: false,
        error: 'TCG error'
      }));

      const result = await socketAiHandler.getHealedSelector('test-socket-id');

      expect(result).to.be.null;
      expect(socketAiHandler.makeRequestTcg.calledOnce).to.be.true;
    });

    it('should handle exceptions and return null', async () => {
      // Mock exception during TCG request with Promise.reject instead of .rejects
      socketAiHandler.makeRequestTcg.returns(Promise.reject(new Error('Network error')));

      const result = await socketAiHandler.getHealedSelector('test-socket-id');

      expect(result).to.be.null;
      expect(socketAiHandler.makeRequestTcg.calledOnce).to.be.true;
    });
  });

  describe('#addAiEnabledSession', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getRegistryObject');
      sinon.stub(socketAiHandler, 'setSessionData');
    });

    afterEach(() => {
      socketAiHandler.getRegistryObject.restore();
      socketAiHandler.setSessionData.restore();
    });

    it('should not add session if socket_id already exists in aiSessions', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);

      socketAiHandler.addAiEnabledSession('test-socket-id', 'test-session-id');

      expect(socketAiHandler.getRegistryObject.called).to.be.false;
      expect(socketAiHandler.setSessionData.called).to.be.false;
    });

    it('should add session with kSelfHeal when enable_ai_healing is true', () => {
      socketAiHandler.getRegistryObject.returns({
        isPlaywright: true,
        aiSessionDetails: {
          enable_ai_healing: 'true',
          ai_soft_heal: 'false'
        }
      });

      socketAiHandler.addAiEnabledSession('test-socket-id', 'test-session-id');

      expect(socketAiHandler.aiSessions.get('test-socket-id')).to.equal(kSelfHeal);
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', RAILS_SESSION_ID, 'test-session-id')).to.be.true;
    });

    it('should add session with kSoftHeal when ai_soft_heal is true and enable_ai_healing is false', () => {
      socketAiHandler.getRegistryObject.returns({
        isPlaywright: true,
        aiSessionDetails: {
          enable_ai_healing: 'false',
          ai_soft_heal: 'true'
        }
      });

      socketAiHandler.addAiEnabledSession('test-socket-id', 'test-session-id');

      expect(socketAiHandler.aiSessions.get('test-socket-id')).to.equal(kSoftHeal);
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', RAILS_SESSION_ID, 'test-session-id')).to.be.true;
    });

    it('should not add session if not a Playwright session', () => {
      socketAiHandler.getRegistryObject.returns({
        isPlaywright: false,
        aiSessionDetails: {
          enable_ai_healing: 'true'
        }
      });

      socketAiHandler.addAiEnabledSession('test-socket-id', 'test-session-id');

      expect(socketAiHandler.aiSessions.has('test-socket-id')).to.be.false;
      expect(socketAiHandler.setSessionData.called).to.be.false;
    });

    it('should handle errors gracefully', () => {
      socketAiHandler.getRegistryObject.throws(new Error('Registry error'));

      socketAiHandler.addAiEnabledSession('test-socket-id', 'test-session-id');

      expect(socketAiHandler.aiSessions.has('test-socket-id')).to.be.false;
      expect(loggerMock.error.calledOnce).to.be.true;
    });
  });

  describe('#getRegistryObject', () => {
    beforeEach(() => {
      // Reset the globalRegistry
      Object.keys(globalRegistryMock).forEach(key => {
        delete globalRegistryMock[key];
      });

      // Setup a test session in the registry
      globalRegistryMock['test-session-id'] = {
        sessionID: 'test-session-id',
        testData: 'test-value'
      };

      // Use rewire to set the global_registry to our mock
      SocketAiHandlerModule.__set__('globalRegistry', globalRegistryMock);
    });

    it('should return the registry object for a valid session ID', () => {
      const result = socketAiHandler.getRegistryObject('test-session-id');

      expect(result).to.deep.equal({
        sessionID: 'test-session-id',
        testData: 'test-value'
      });
    });

    it('should throw an error for undefined session ID', () => {
      expect(() => socketAiHandler.getRegistryObject(undefined)).to.throw('socketId is undefined');
    });

    it('should return undefined for non-existent session ID', () => {
      const result = socketAiHandler.getRegistryObject('non-existent-id');

      expect(result).to.be.undefined;
    });
  });

  describe('#isHealingResponse', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getSessionData');

      // Reset aiSessions
      socketAiHandler.aiSessions.clear();
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
    });

    it('should return false if socket_id is not in aiSessions', () => {
      const result = socketAiHandler.isHealingResponse('non-existent-id', { id: 123 });

      expect(result).to.be.false;
      expect(socketAiHandler.getSessionData.called).to.be.false;
    });

    it('should return false if response has no ID', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);

      const result = socketAiHandler.isHealingResponse('test-socket-id', {});

      expect(result).to.be.false;
    });

    it('should return false if original request has no ID', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);
      socketAiHandler.getSessionData.returns({});

      const result = socketAiHandler.isHealingResponse('test-socket-id', { id: 123 });

      expect(result).to.be.false;
      expect(socketAiHandler.getSessionData.calledWith('test-socket-id', kOriginalRequest)).to.be.true;
    });

    it('should return true if response ID is above the base healing ID value', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);
      socketAiHandler.getSessionData.returns({ id: BASE_HEALING_REQUEST_ID_VALUE + 1 });

      const result = socketAiHandler.isHealingResponse('test-socket-id', { id: BASE_HEALING_REQUEST_ID_VALUE + 1 });

      expect(result).to.be.true;
    });

    it('should return true if response ID matches original request ID', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);
      socketAiHandler.getSessionData.returns({ id: 123 });

      const result = socketAiHandler.isHealingResponse('test-socket-id', { id: 123 });

      expect(result).to.be.true;
    });

    it('should return false if response ID is below base value and doesn\'t match original', () => {
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);
      socketAiHandler.getSessionData.returns({ id: 123 });

      const result = socketAiHandler.isHealingResponse('test-socket-id', { id: 456 });

      expect(result).to.be.false;
    });
  });

  describe('#getNewHealingRequestId', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'setSessionData');
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.setSessionData.restore();
    });

    it('should return base value for first request', () => {
      socketAiHandler.getSessionData.returns(undefined);

      const result = socketAiHandler.getNewHealingRequestId('test-socket-id');

      expect(result).to.equal(BASE_HEALING_REQUEST_ID_VALUE);
      expect(socketAiHandler.getSessionData.calledWith('test-socket-id', kHealingRequestId)).to.be.true;
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kHealingRequestId, BASE_HEALING_REQUEST_ID_VALUE)).to.be.true;
    });

    it('should increment existing request ID', () => {
      socketAiHandler.getSessionData.returns(BASE_HEALING_REQUEST_ID_VALUE);

      const result = socketAiHandler.getNewHealingRequestId('test-socket-id');

      expect(result).to.equal(BASE_HEALING_REQUEST_ID_VALUE + 1);
      expect(socketAiHandler.getSessionData.calledWith('test-socket-id', kHealingRequestId)).to.be.true;
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kHealingRequestId, BASE_HEALING_REQUEST_ID_VALUE + 1)).to.be.true;
    });
  });

  describe('#executeScriptRequest', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'setSessionData');
      sinon.stub(socketAiHandler, 'isPlaywrightRequestSuccess');
      sinon.stub(socketAiHandler, 'sendToTcgScript').returns('test-script');
      sinon.stub(socketAiHandler, 'getNewHealingRequestId').returns(100001);
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.setSessionData.restore();
      socketAiHandler.isPlaywrightRequestSuccess.restore();
      socketAiHandler.sendToTcgScript.restore();
      socketAiHandler.getNewHealingRequestId.restore();
    });

    it('should set healing status to EXECUTE_SCRIPT', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(true);
      socketAiHandler.getSessionData.returns({
        guid: 'test-guid',
        metadata: {},
        params: { selector: '#test-selector' }
      });

      socketAiHandler.executeScriptRequest('test-socket-id', response, handlerMock);

      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kCurrentHealingStatus, HEALING_STATUS.EXECUTE_SCRIPT)).to.be.true;
    });

    it('should determine request success and set in session data', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(true);
      socketAiHandler.getSessionData.returns({
        guid: 'test-guid',
        metadata: {},
        params: { selector: '#test-selector' }
      });

      socketAiHandler.executeScriptRequest('test-socket-id', response, handlerMock);

      expect(socketAiHandler.isPlaywrightRequestSuccess.calledWith(response)).to.be.true;
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kWaitForRequestSuccess, true)).to.be.true;
    });

    it('should get script from sendToTcgScript and send it to terminal', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(true);
      socketAiHandler.getSessionData.returns({
        guid: 'test-guid',
        metadata: { location: 'test-location' },
        params: { selector: '#test-selector' }
      });

      socketAiHandler.executeScriptRequest('test-socket-id', response, handlerMock);

      expect(socketAiHandler.sendToTcgScript.calledWith('test-socket-id', true, '#test-selector')).to.be.true;
      expect(handlerMock.sendToTerminal.calledOnce).to.be.true;

      // Check the request data structure
      const requestData = handlerMock.sendToTerminal.firstCall.args[0];
      expect(requestData.id).to.equal(100001);
      expect(requestData.guid).to.equal('test-guid');
      expect(requestData.method).to.equal('evaluateExpression');
      expect(requestData.params.expression).to.include('test-script');
      expect(requestData.metadata.apiName).to.equal('page.evaluate');
    });

    it('should not proceed if original request is missing required fields', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(true);
      socketAiHandler.getSessionData.returns({
        params: { selector: '#test-selector' }
        // Missing guid and metadata
      });

      socketAiHandler.executeScriptRequest('test-socket-id', response, handlerMock);

      expect(socketAiHandler.sendToTcgScript.called).to.be.false;
      expect(handlerMock.sendToTerminal.called).to.be.false;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });
  });

  describe('#healedSelectorRequest', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'setSessionData');
      sinon.stub(socketAiHandler, 'getHealedSelector');
      sinon.stub(socketAiHandler, 'originalSelectorRequest');
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.setSessionData.restore();
      socketAiHandler.getHealedSelector.restore();
      socketAiHandler.originalSelectorRequest.restore();
    });

    it('should set healing status to HEALED_SELECTOR', async () => {
      socketAiHandler.getSessionData.returns(true); // waitForRequestSuccess is true

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kCurrentHealingStatus, HEALING_STATUS.HEALED_SELECTOR)).to.be.true;
    });

    it('should use original selector if waitForRequestSuccess is true', async () => {
      socketAiHandler.getSessionData.returns(true); // waitForRequestSuccess is true

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.originalSelectorRequest.calledWith('test-socket-id', handlerMock)).to.be.true;
      expect(socketAiHandler.getHealedSelector.called).to.be.false;
    });

    it('should get healed selector and use it if waitForRequestSuccess is false', async () => {
      // First call for waitForRequestSuccess, second for original request
      socketAiHandler.getSessionData.onFirstCall().returns(false); // waitForRequestSuccess is false
      socketAiHandler.getSessionData.onSecondCall().returns({
        params: {}
      });
      socketAiHandler.getHealedSelector.returns(Promise.resolve('#healed-selector'));

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.getHealedSelector.calledWith('test-socket-id')).to.be.true;
      expect(handlerMock.sendToTerminal.calledOnce).to.be.true;
    });

    it('should fall back to original selector if no healed selector is available', async () => {
      socketAiHandler.getSessionData.returns(false); // waitForRequestSuccess is false
      socketAiHandler.getHealedSelector.returns(Promise.resolve(null));

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.originalSelectorRequest.calledWith('test-socket-id', handlerMock)).to.be.true;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });

    it('should fall back to original selector if original request has no params', async () => {
      socketAiHandler.getSessionData.onFirstCall().returns(false); // waitForRequestSuccess is false
      socketAiHandler.getSessionData.onSecondCall().returns({}); // No params in original request
      socketAiHandler.getHealedSelector.returns(Promise.resolve('#healed-selector'));

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.originalSelectorRequest.calledWith('test-socket-id', handlerMock)).to.be.true;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });

    it('should handle errors and fall back to original selector', async () => {
      socketAiHandler.getSessionData.returns(false); // waitForRequestSuccess is false
      socketAiHandler.getHealedSelector.returns(Promise.reject(new Error('Test error')));

      await socketAiHandler.healedSelectorRequest('test-socket-id', {}, handlerMock);

      expect(socketAiHandler.originalSelectorRequest.calledWith('test-socket-id', handlerMock)).to.be.true;
      expect(loggerMock.error.calledOnce).to.be.true;
    });
  });

  describe('#originalSelectorRequest', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'setSessionData');
      sinon.stub(socketAiHandler, 'getRegistryObject');

      // Mock the necessary session data to avoid the issue with keyObject
      socketAiHandler.getSessionData.withArgs('test-socket-id', RAILS_SESSION_ID).returns('test-session-id');
      socketAiHandler.getRegistryObject.withArgs('test-session-id').returns({
        playwrightAiMetrics: { cumulativeHealingTime: 0 },
        ai_healing_details: {}
      });
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.setSessionData.restore();
      socketAiHandler.getRegistryObject.restore();
    });

    it('should set healing status to ORIGINAL_SELECTOR', () => {
      socketAiHandler.getSessionData.withArgs('test-socket-id', kOriginalRequest).returns({ id: 123 });

      socketAiHandler.originalSelectorRequest('test-socket-id', handlerMock);

      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kCurrentHealingStatus, HEALING_STATUS.ORIGINAL_SELECTOR)).to.be.true;
    });

    it('should send original request to terminal', () => {
      const originalRequest = { id: 123, method: 'fill' };
      socketAiHandler.getSessionData.withArgs('test-socket-id', kOriginalRequest).returns(originalRequest);

      socketAiHandler.originalSelectorRequest('test-socket-id', handlerMock);

      expect(handlerMock.sendToTerminal.calledWith(originalRequest, 'test-socket-id')).to.be.true;
    });

    it('should log warning if original request data is missing', () => {
      socketAiHandler.getSessionData.returns(null);

      socketAiHandler.originalSelectorRequest('test-socket-id', handlerMock);

      expect(handlerMock.sendToTerminal.called).to.be.false;
      expect(loggerMock.warn.calledOnce).to.be.true;
    });
  });

  describe('#handleWaitForResponse', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'executeScriptRequest');
    });

    afterEach(() => {
      socketAiHandler.executeScriptRequest.restore();
    });

    it('should call executeScriptRequest with correct parameters', () => {
      const response = { id: 123 };

      socketAiHandler.handleWaitForResponse('test-socket-id', response, handlerMock);

      expect(socketAiHandler.executeScriptRequest.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });
  });

  describe('#handleExecuteScriptResponse', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'healedSelectorRequest');
    });

    afterEach(() => {
      socketAiHandler.healedSelectorRequest.restore();
    });

    it('should call healedSelectorRequest with correct parameters', () => {
      const response = { id: 123 };

      socketAiHandler.handleExecuteScriptResponse('test-socket-id', response, handlerMock);

      expect(socketAiHandler.healedSelectorRequest.calledWith('test-socket-id', response, handlerMock)).to.be.true;
    });
  });

  describe('#handleHealedSelectorResponse', () => {
    let handlerMock;

    beforeEach(() => {
      handlerMock = {
        sendToTerminal: sinon.stub()
      };

      sinon.stub(socketAiHandler, 'isPlaywrightRequestSuccess');
      sinon.stub(socketAiHandler, 'originalSelectorRequest');
      sinon.stub(socketAiHandler, 'recordHealingSuccess');
    });

    afterEach(() => {
      socketAiHandler.isPlaywrightRequestSuccess.restore();
      socketAiHandler.originalSelectorRequest.restore();
      socketAiHandler.recordHealingSuccess.restore();
    });

    it('should return false and fall back to original if request failed', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(false);

      const result = socketAiHandler.handleHealedSelectorResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.false;
      expect(socketAiHandler.originalSelectorRequest.calledWith('test-socket-id', handlerMock)).to.be.true;
      expect(socketAiHandler.recordHealingSuccess.called).to.be.false;
    });

    it('should return true and record success if request was successful', () => {
      const response = { id: 123 };
      socketAiHandler.isPlaywrightRequestSuccess.returns(true);

      const result = socketAiHandler.handleHealedSelectorResponse('test-socket-id', response, handlerMock);

      expect(result).to.be.true;
      expect(socketAiHandler.recordHealingSuccess.calledWith('test-socket-id')).to.be.true;
      expect(socketAiHandler.originalSelectorRequest.called).to.be.false;
    });
  });

  describe('#handleOriginalSelectorResponse', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'setSessionData');
    });

    afterEach(() => {
      socketAiHandler.setSessionData.restore();
    });

    it('should clear healing data', () => {
      const response = { id: 123 };

      socketAiHandler.handleOriginalSelectorResponse('test-socket-id', response);

      // Check that we've cleared all the healing data
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kOriginalRequest, null)).to.be.true;
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kCurrentHealingStatus, null)).to.be.true;
      expect(socketAiHandler.setSessionData.calledWith('test-socket-id', kWaitForRequestSuccess, null)).to.be.true;
    });
  });

  describe('#makeRequestTcg', () => {
    let sleep;

    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'getRegistryObject');
      // Setup AICommandHelper mock in the module
      SocketAiHandlerModule.__set__('AICommandHelper', AICommandHelperMock);
      // Stub sleep function
      SocketAiHandlerModule.__set__('sleep', sinon.stub().returns(Promise.resolve()));
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.getRegistryObject.restore();
      AICommandHelperMock.makeRequestTcg.reset();
    });

    it('should call TCG API with correct parameters', async () => {
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({
        aiTcgHostname: 'test-tcg-hostname'
      });

      const tcgResponse = {
        statusCode: 200,
        data: JSON.stringify({
          success: true,
          data: { css: '#healed-selector' }
        })
      };

      AICommandHelperMock.makeRequestTcg.returns(Promise.resolve(tcgResponse));

      const result = await socketAiHandler.makeRequestTcg('test-socket-id');

      expect(socketAiHandler.getSessionData.calledWith('test-socket-id', RAILS_SESSION_ID)).to.be.true;
      expect(AICommandHelperMock.makeRequestTcg.calledWith(
        TCG_ENDPOINTS.GET_RESULT.method,
        TCG_ENDPOINTS.GET_RESULT.path,
        `{"data":{"sessionId":"test-session-id"}}`,
        TCG_HEADERS,
        'test-tcg-hostname'
      )).to.be.true;

      expect(result).to.deep.equal({
        success: true,
        data: { css: '#healed-selector' }
      });
    });

    it('should retry when TCG API returns non-200 status code', async () => {
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({});

      const errorResponse = {
        statusCode: 500,
        data: '{}'
      };

      const successResponse = {
        statusCode: 200,
        data: JSON.stringify({
          success: true,
          data: { css: '#healed-selector' }
        })
      };

      // First call fails, second call succeeds
      AICommandHelperMock.makeRequestTcg.onFirstCall().returns(Promise.resolve(errorResponse));
      AICommandHelperMock.makeRequestTcg.onSecondCall().returns(Promise.resolve(successResponse));

      const result = await socketAiHandler.makeRequestTcg('test-socket-id');

      expect(AICommandHelperMock.makeRequestTcg.calledTwice).to.be.true;
      const sleep = SocketAiHandlerModule.__get__('sleep');
      expect(sleep.calledOnce).to.be.true;
      expect(sleep.calledWith(TCG_ENDPOINTS.GET_RESULT.sleep)).to.be.true;

      expect(result).to.deep.equal({
        success: true,
        data: { css: '#healed-selector' }
      });
    });

    it('should handle API errors and retry', async () => {
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({});

      AICommandHelperMock.makeRequestTcg.onFirstCall().returns(Promise.reject(new Error('API error')));
      AICommandHelperMock.makeRequestTcg.onSecondCall().returns(Promise.resolve({
        statusCode: 200,
        data: JSON.stringify({
          success: true,
          data: { css: '#healed-selector' }
        })
      }));

      const result = await socketAiHandler.makeRequestTcg('test-socket-id');

      expect(AICommandHelperMock.makeRequestTcg.calledTwice).to.be.true;
      expect(loggerMock.error.calledOnce).to.be.true;

      expect(result).to.deep.equal({
        success: true,
        data: { css: '#healed-selector' }
      });
    });

    it('should return failure after exhausting all retries', async () => {
      socketAiHandler.getSessionData.returns('test-session-id');
      socketAiHandler.getRegistryObject.returns({});

      // The first and second calls return error responses
      AICommandHelperMock.makeRequestTcg.onFirstCall().returns(Promise.resolve({
        statusCode: 500,
        data: '{}'
      }));
      AICommandHelperMock.makeRequestTcg.onSecondCall().returns(Promise.resolve({
        statusCode: 500,
        data: '{}'
      }));
      // The third call also returns an error
      AICommandHelperMock.makeRequestTcg.onThirdCall().returns(Promise.resolve({
        statusCode: 500,
        data: '{}'
      }));

      // Mock the retries value to a small number to make the test faster
      const originalRetries = TCG_ENDPOINTS.GET_RESULT.retries;
      TCG_ENDPOINTS.GET_RESULT.retries = 2;

      const result = await socketAiHandler.makeRequestTcg('test-socket-id');

      // Reset retries value
      TCG_ENDPOINTS.GET_RESULT.retries = originalRetries;

      const sleep = SocketAiHandlerModule.__get__('sleep');
      expect(AICommandHelperMock.makeRequestTcg.callCount).to.equal(2);
      expect(sleep.callCount).to.equal(2);

      expect(result).to.deep.equal({
        success: false,
        data: null
      });
    });
  });

  describe('#recordHealingSuccess', () => {
    beforeEach(() => {
      sinon.stub(socketAiHandler, 'getSessionData');
      sinon.stub(socketAiHandler, 'getRegistryObject');
    });

    afterEach(() => {
      socketAiHandler.getSessionData.restore();
      socketAiHandler.getRegistryObject.restore();
    });

    it('should handle soft healing success case correctly', () => {
      const keyObject = {};
      socketAiHandler.aiSessions.set('test-socket-id', kSoftHeal);
      socketAiHandler.getSessionData.withArgs('test-socket-id', RAILS_SESSION_ID).returns('test-session-id');
      socketAiHandler.getSessionData.withArgs('test-socket-id', kHealedSelector).returns('css=#test-selector');
      socketAiHandler.getRegistryObject.returns(keyObject);

      socketAiHandler.recordHealingSuccess('test-socket-id');
      expect(keyObject.softHealingSuccess).to.be.equal('true');
      expect(keyObject.healedSelector).to.deep.equal({
        using: 'css',
        value: '#test-selector',
        selfHeal: false
      });
    });

    it('should handle self healing success case correctly', () => {
      // Set up test data
      const keyObject = {};
      socketAiHandler.aiSessions.set('test-socket-id', kSelfHeal);

      // Proper stubbing
      socketAiHandler.getSessionData.withArgs('test-socket-id', RAILS_SESSION_ID).returns('test-session-id');
      socketAiHandler.getSessionData.withArgs('test-socket-id', kOriginalRequest).returns({
        params: { selector: 'css=#test-selector' }
      });

      socketAiHandler.getRegistryObject.returns(keyObject);

      // Execute method
      socketAiHandler.recordHealingSuccess('test-socket-id');

      // Verify results
      expect(keyObject.selfHealingSuccess).to.be.equal('true');
      expect(keyObject.healedSelector).to.deep.equal({
        using: 'css',
        value: '#test-selector',
        selfHeal: true
      });
    });


    it('should handle selector with multiple = characters', () => {
      // Set up test data
      const keyObject = {};
      socketAiHandler.aiSessions.set('test-socket-id', kSoftHeal);
      socketAiHandler.getSessionData.withArgs('test-socket-id', RAILS_SESSION_ID).returns('test-session-id');
      socketAiHandler.getSessionData.withArgs('test-socket-id', kHealedSelector).returns('xpath=//input[@type="text" and @value="test=value"]');
      socketAiHandler.getRegistryObject.returns(keyObject);

      // Execute method
      socketAiHandler.recordHealingSuccess('test-socket-id');

      // Verify results
      expect(keyObject.healedSelector).to.deep.equal({
        using: 'xpath',
        value: '//input[@type="text" and @value="test=value"]',
        selfHeal: false
      });
    });


    it('should not overwrite existing success flags', () => {
      // Set up test data
      const keyObject = {
        softHealingSuccess: true,
        selfHealingSuccess: true
      };
      socketAiHandler.aiSessions.set('test-socket-id', kSoftHeal);
      socketAiHandler.getSessionData.withArgs('test-socket-id', RAILS_SESSION_ID).returns('test-session-id');
      socketAiHandler.getSessionData.withArgs('test-socket-id', kHealedSelector).returns('css=#test-selector');
      socketAiHandler.getRegistryObject.returns(keyObject);

      // Execute method
      socketAiHandler.recordHealingSuccess('test-socket-id');

      // Verify flags weren't changed
      expect(keyObject.softHealingSuccess).to.be.true;
      expect(keyObject.selfHealingSuccess).to.be.true;
    });
  });
});
